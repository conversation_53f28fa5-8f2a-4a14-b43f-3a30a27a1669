/* Styles specific to the thumbnail preview area */

.preview-container-override {
    /* Example - unlikely needed with Tailwind */
    box-shadow: 0 0 15px rgba(128, 0, 128, 0.5); 
}

.placeholder-style {
	   /* Another mixed indent */
    font-style: italic;
}

/* More dead CSS */
/* #preview-image { transition: transform 0.5s; } */ 

.preview-container {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
   
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Add dark background only when there's an image */
.preview-container.has-image {
    background: #1F2937; /* Dark background only when image is present */
}

/* Ensure preview container maintains size on all screen sizes */
@media (max-width: 820px) {
    .preview-container {
        width: 100%;
        max-width: 100%;
        height: auto;
        aspect-ratio: 16/9; /* Maintain aspect ratio */
        min-height: 180px; /* Prevent collapse */
    }
    
    .modal-thumbnail-image {
        object-fit: cover !important;
        width: min(1088px, 85vw) !important;
        height: auto !important;
        aspect-ratio: 1.498 !important;
        max-height: min(614px, 55vh) !important;
    }
}

/* Generated thumbnail styling for all contexts - UNIFIED MODAL LOGIC */
.generated-thumbnail,
.preview-container img {
    width: 100%;
    height: 100%;
    aspect-ratio: 16/9 !important;
    object-fit: contain !important; /* Desktop default: use contain like modal-image-container */
    display: block;
    border-radius: 0;
    image-rendering: auto; /* Ensure crisp rendering */
}

/* Modal thumbnail gets special treatment to show full image */
.modal-thumbnail-image {
    display: block;
    image-rendering: auto; /* Ensure crisp rendering */
}

/* Modal specific sizing - Optimized canvas size with cover fit */
.modal-thumbnail-image {
    /* Optimized canvas size at 1088×612 pixels (85% of 1280×720, maintaining 16:9 ratio) */
    width: 1088px !important;
    height: 612px !important;
    border-radius: 8px;
    background: transparent; /* Transparent background to match downloaded images */
    object-fit: cover; /* Fill entire canvas while maintaining aspect ratio */
    display: block;
}

/* Mobile container adjustments for proper aspect ratio */
@media (max-width: 768px) {
    .modal-image-container {
        width: 100%;
        aspect-ratio: 16/9;
        max-height: none;
        overflow: hidden;
    }
    
    .preview-container {
        aspect-ratio: 16/9;
        width: 100%;
        height: auto;
        max-height: none; /* Remove max-height constraint that was causing clipping */
        min-height: 200px; /* Ensure minimum height for proper display */
    }
    
    /* Mobile: Use contain to prevent clipping, maintain full image visibility */
    .generated-thumbnail,
    .preview-container img {
        object-fit: contain !important; /* Mobile: show full image without cropping */
        background: transparent !important; /* Transparent background to match downloaded images */
        width: 100% !important;
        height: 100% !important;
        aspect-ratio: 16/9 !important;
    }
    
    /* Mobile modal image - full resolution canvas size */
    .modal-thumbnail-image {
        object-fit: cover !important; /* Mobile: fill canvas while maintaining aspect ratio */
        background: transparent !important; /* Transparent background to match downloaded images */
        width: min(920px, 90vw) !important;
        height: auto !important;
        max-height: min(614px, 60vh) !important;
    }
    
    /* Thumbnail Preview Empty State - Mobile Responsive Layout */
    .thumbnail-preview-empty-state-container {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 1.75rem !important; /* 28px gap for clear separation */
        min-height: 220px !important; /* Ensure vertical centering in preview */
        width: 100%;
        height: 100%;
    }
    
    .thumbnail-preview-empty-state-icon {
        margin-bottom: 0 !important;
    }
    
    .thumbnail-preview-empty-state-label {
        margin: 0 !important;
        text-align: center !important;
        font-size: 1.15rem !important;
        color: #8b8fa3 !important;
        font-weight: 500;
    }
}

/* Enhanced Maximize Button with UX Improvements */
.thumbnail-maximize-container {
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    width: 100px !important; /* Increased interaction area */
    height: 80px !important;
    z-index: 30 !important;
    pointer-events: none !important; /* Allow clicks to pass through container */
}

/* Gradient feather overlay - enhanced dark fade for better icon visibility */
.gradient-feather {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 70px !important;
    background: linear-gradient(to bottom, rgba(10, 10, 15, 0.95), rgba(15, 15, 20, 0.6) 50%, rgba(20, 20, 30, 0)) !important;
    pointer-events: none !important;
    z-index: 1 !important;
    opacity: 0 !important; /* Start invisible */
    transition: opacity 250ms cubic-bezier(0.4, 0, 0.2, 1) !important; /* Smooth fade in/out */
}

/* Full preview container gradient overlay - similar to history-item-gradient but top-to-bottom */
.preview-container-gradient {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.3) 70%, rgba(0, 0, 0, 0.7) 100%) !important;
    pointer-events: none !important;
    z-index: 2 !important;
    opacity: 0 !important; /* Start invisible */
    border-radius: 8px !important;
    transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1) !important; /* Smooth fade in/out */
}

/* Maximize button with enhanced opacity behavior */
.thumbnail-maximize-btn {
    position: absolute !important;
    top: 12px !important;
    right: 12px !important;
    z-index: 35 !important;
    background: transparent !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    padding: 8px !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1) !important;
    /* Default state: increased opacity by 15% (0.15 + 0.15 = 0.30) */
    opacity: 0.5 !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
    pointer-events: auto !important; /* Button itself should capture clicks */
    will-change: transform, opacity, background, backdrop-filter !important;
}

/* Thumbnail area hover - button and gradient become fully visible */
.thumbnail-area:hover .thumbnail-maximize-btn,
.thumbnail-maximize-btn:focus-visible {
    opacity: 1 !important;
}

/* Gradient feather visibility on thumbnail hover */
.thumbnail-area:hover .gradient-feather {
    opacity: 1 !important;
}

/* Preview container gradient visibility on hover - only when image is present */
.preview-container.has-image:hover .preview-container-gradient {
    opacity: 1 !important;
}

/* Button hover - opacity drops by 20% and backdrop blur appears */
.thumbnail-maximize-btn:hover,
.thumbnail-maximize-btn:focus-visible {
    opacity: 1 !important; /* 20% lower than full opacity */
    backdrop-filter: blur(6px) !important;
    -webkit-backdrop-filter: blur(6px) !important;
    background: rgba(20, 20, 30, 0.25) !important; /* Glassy dark background */
    border-color: rgba(255, 255, 255, 0.2) !important;
}

.thumbnail-maximize-btn:focus-visible {
    outline: 2px solid #a78bfa !important;
    outline-offset: 2px !important;
}

.thumbnail-maximize-btn:active {
    opacity: 0.6 !important;
    transform: scale(0.95) !important;
}

/* Password visibility toggle button */
.password-toggle-btn {
    position: absolute !important;
    right: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 20 !important;
    background: transparent !important;
    border: none !important;
    color: rgb(156, 163, 175) !important; /* text-gray-400 */
    padding: 4px !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    /* Ensure button stays within input bounds */
    pointer-events: auto !important;
    will-change: transform, color !important;
}

.password-toggle-btn:hover {
    color: rgb(209, 213, 219) !important; /* text-gray-300 */
    background: rgba(75, 85, 99, 0.1) !important;
}

.password-toggle-btn:focus {
    outline: 2px solid #a78bfa !important;
    outline-offset: 2px !important;
}

.password-toggle-btn:active {
    transform: translateY(-50%) scale(0.95) !important;
}

/* Password input field container */
.password-input-container {
    position: relative !important;
    /* Ensure proper stacking context for toggle button */
    isolation: isolate !important;
}

/* Modal close button */
.modal-close-btn-fixed {
    position: absolute !important;
    top: 12px !important;
    right: 12px !important;
    z-index: 60 !important;
    background: transparent !important;
    border: none !important;
    color: rgb(156, 163, 175) !important; /* text-gray-400 */
    padding: 8px !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    /* Ensure button stays within modal bounds */
    transform: none !important;
    will-change: transform, background-color, color !important;
    pointer-events: auto !important;
}

.modal-close-btn-fixed:hover {
    color: white !important;
    background: rgba(75, 85, 99, 0.5) !important;
    transform: scale(1.05) !important;
}

.modal-close-btn-fixed:focus {
    outline: 2px solid #a78bfa !important;
    outline-offset: 2px !important;
}

.modal-close-btn-fixed:active {
    transform: scale(0.95) !important;
}

/* Modal header container */
.modal-header-container {
    position: relative !important;
    /* Ensure proper stacking context for close button */
    isolation: isolate !important;
}

/* Upgrade CTA Button with Shimmer Effect */
.upgrade-cta-btn,
.welcome-signin-btn {
    background: #006FEE !important;
    color: #fff !important;
    position: relative !important;
    overflow: hidden !important;
    transition: background 0.25s cubic-bezier(0.4,0,0.2,1), box-shadow 0.25s, color 0.2s !important;
    border: none !important;
    /* Remove any existing gradient backgrounds */
    background-image: none !important;
}

.upgrade-cta-btn:hover,
.upgrade-cta-btn:focus-visible,
.welcome-signin-btn:hover,
.welcome-signin-btn:focus-visible {
    background: #1A8CFF !important;
    box-shadow: 0 8px 25px rgba(0, 111, 238, 0.3) !important;
}

.upgrade-cta-btn:active,
.welcome-signin-btn:active {
    background: #0056CC !important;
    transform: scale(0.98) !important;
}

.upgrade-cta-btn:disabled,
.welcome-signin-btn:disabled {
    background: #6B7280 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

.upgrade-cta-btn:disabled::before,
.welcome-signin-btn:disabled::before {
    display: none !important;
}

.upgrade-cta-btn:disabled:hover,
.welcome-signin-btn:disabled:hover {
    background: #6B7280 !important;
    box-shadow: none !important;
    min-height: 50px;
}

.upgrade-cta-btn::before,
.welcome-signin-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -75%;
    width: 50%;
    height: 100%;
    background: linear-gradient(120deg, rgba(255,255,255,0.18) 0%, rgba(255,255,255,0.08) 100%);
    transform: skewX(-20deg);
    transition: none;
    pointer-events: none;
    opacity: 0;
}

.upgrade-cta-btn:hover::before,
.welcome-signin-btn:hover::before {
    animation: shimmer-move 0.7s cubic-bezier(0.4,0,0.2,1);
}

@keyframes shimmer-move {
    0% { 
        left: -75%; 
        opacity: 1;
    }
    100% { 
        left: 125%; 
        opacity: 0;
    }
}

/* Sign in button text styling */
.signin-button-text {
    font-size: 1.1rem !important;
    font-weight: 500 !important;
}

/* Responsive adjustments for upgrade CTA button and welcome signin button */
@media (max-width: 1024px) {
    .upgrade-cta-btn,
    .welcome-signin-btn {
        padding: 12px 20px !important;
        font-size: 14px !important;
    }
    
    .signin-button-text {
        font-size: 1rem !important;
    }
}

@media (max-width: 768px) {
    .upgrade-cta-btn,
    .welcome-signin-btn {
        padding: 10px 16px !important;
        font-size: 13px !important;
    }
    
    .signin-button-text {
        font-size: 0.95rem !important;
    }
    
    .upgrade-cta-btn::before,
    .welcome-signin-btn::before {
        width: 60%;
        left: -80%;
    }
    
    @keyframes shimmer-move {
        0% { 
            left: -80%; 
            opacity: 1;
        }
        100% { 
            left: 130%; 
            opacity: 0;
        }
    }
}

/* Mobile portrait specific styling for welcome signin button - Updated for 576px breakpoint */
@media (max-width: 576px) and (orientation: portrait) {
    #welcome-signin-submit-btn {
        padding: 0.875rem 1rem !important; /* 14px top/bottom, 16px left/right - matches auth-cta-btn */
        min-height: 50px !important; /* Unified 50px height */
        font-size: 0.875rem !important; /* Match auth-cta-btn font size */
    }
    
    /* 25% larger font size for better readability in mobile portrait */
    #welcome-signin-submit-btn .signin-button-text,
    #welcome-signin-submit-btn .signin-loading-text,
    #welcome-signin-submit-btn .auth-cta-text,
    #welcome-signin-submit-btn .auth-loading-text {
        font-size: 1.09375rem !important; /* 0.875rem * 1.25 = 1.09375rem (25% increase) */
        line-height: 1.3 !important; /* Optimized for button height */
    }
    
    /* Additional override for general signin-button-text class */
    .signin-button-text,
    .signin-loading-text {
        font-size: 1.09375rem !important; /* 25% larger than base mobile size */
        line-height: 1.3 !important;
        font-weight: 500 !important;
    }
}

/* Preview wrapper to contain the preview */
.preview-wrapper {
    width: 100%;
    max-width: 652px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Ensure thumbnail preview wrapper maintains proper sizing on mobile */
@media (max-width: 768px) {
    #thumbnail-preview-wrapper {
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 auto !important;
        overflow: visible !important;
    }
    
    #thumbnail-preview-wrapper .preview-container {
        width: 100% !important;
        height: auto !important;
        min-height: 200px !important;
        max-height: none !important;
        overflow: hidden !important;
    }
}

/* Optional debug grid to detect gaps – enable during dev only */
/*
.preview-container::before {
    content: "";
    background: repeating-linear-gradient(45deg, #f00, #f00 1px, #000 1px, #000 10px);
    opacity: 0.1;
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 0;
}
*/ 

/* Full-size preview modal overlay */
.full-preview-modal {
    position: fixed;
    inset: 0;
    z-index: 50;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0,0,0,0.7);
    backdrop-filter: blur(8px);
    transition: opacity 0.3s;
    animation: fadeIn 0.3s;
}

.full-preview-modal-content {
    background: #18181b;
    border-radius: 1rem;
    box-shadow: 0 8px 32px rgba(0,0,0,0.5);
    padding: 1.5rem;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    animation: fadeIn 0.3s;
}

/* Ensure full preview modal image container has proper aspect ratio */
.full-preview-modal-content .modal-image-container,
.full-preview-modal-content > div {
    width: min(1088px, 68vw) !important;
    aspect-ratio: 16/9 !important;
    overflow: hidden !important;
}

.full-preview-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: #fff;
    font-size: 2rem;
    cursor: pointer;
    z-index: 2;
    transition: color 0.2s;
}
.full-preview-modal-close:hover {
    color: #60a5fa;
}

.full-preview-modal-img {
    width: 100%;
    height: auto;
    max-height: 80vh;
    border-radius: 0.75rem;
    box-shadow: 0 4px 16px rgba(0,0,0,0.3);
    object-fit: cover !important;
    background: #18181b;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
} 

.finalizing-shimmer {
  position: relative;
  background: linear-gradient(90deg, 
    #e5e7eb 0%, 
    #f3f4f6 20%, 
    #ffffff 50%, 
    #f3f4f6 80%, 
    #e5e7eb 100%
  );
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: shimmerEffect 2s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.3));
}

@keyframes shimmerEffect {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

/* Animated dots for finalizing text */
.finalizing-text::after {
  content: '';
  animation: finalizingDots 1.5s steps(4, end) infinite;
}

@keyframes finalizingDots {
  0% { content: ''; }
  25% { content: '.'; }
  50% { content: '..'; }
  75% { content: '...'; }
  100% { content: ''; }
} 

/* Thumbnail Preview Modal */
.thumbnail-preview-modal-backdrop {
    position: fixed;
    inset: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    animation: fadeIn 0.2s ease-out;
    padding: 20px;
    overflow: auto; /* Allow scrolling if content (1088×612) is larger than viewport */
}

.thumbnail-preview-modal-content {
    position: relative;
    width: fit-content;
    height: fit-content;
    animation: modalSlideIn 0.25s ease-out;
    display: flex;
    flex-direction: column;
    /* Allow optimized 1088×612 size with responsive constraints */
    max-width: none;
    max-height: none;
    overflow: hidden;
}

.thumbnail-preview-modal-backdrop.closing {
    animation: backdropFadeOut 0.25s ease-out forwards;
}

.thumbnail-preview-modal-content.closing {
    animation: modalSlideOut 0.25s ease-out forwards;
}

/* Enhanced macOS-style title bar with premium glassy effect */
.modal-title-bar {
    background: linear-gradient(135deg, rgba(45, 45, 75, 0.9), rgba(30, 30, 50, 0.8)); /* Premium gradient background */
    backdrop-filter: blur(12px) saturate(1.8); /* Enhanced blur with saturation for premium glass effect */
    -webkit-backdrop-filter: blur(12px) saturate(1.8);
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    padding: 10px 20px; /* Reduced from 12px by 15% (12 * 0.85 = 10.2 ≈ 10px) */
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15); /* Slightly more prominent border */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1); /* Inner light reflection for glass effect */
    min-height: 37px; /* Reduced from 44px by 15% (44 * 0.85 = 37.4 ≈ 37px) */
    transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1); /* Premium transitions for all properties */
}

/* Traffic lights container */
.traffic-lights {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Individual traffic light buttons */
.traffic-light {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Red traffic light (decorative - dark grey) */
.traffic-light.red {
    background: rgba(75, 85, 99, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Yellow traffic light (decorative - dark grey) */
.traffic-light.yellow {
    background: rgba(75, 85, 99, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Green traffic light (decorative - dark grey) */
.traffic-light.green {
    background: rgba(75, 85, 99, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Traffic lights are now decorative only - no hover effects */

/* Close button styling is now handled by Tailwind classes matching template modal */

.modal-image-container {
    position: relative;
    display: inline-block;
    background: transparent; /* Transparent background to match downloaded images */
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    overflow: visible; /* Allow full image to be visible without cropping */
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    /* Ensure proper stacking context for buttons */
    isolation: isolate;
    /* Container should wrap the optimized 1088×612 image size */
    width: 1088px;
    height: 612px;
    line-height: 0; /* Prevent extra spacing around image */
}

/* Mobile container adjustments */
@media (max-width: 768px) {
    .modal-image-container {
        width: min(1088px, 90vw);
        height: auto;
        max-height: min(614px, 60vh);
        display: inline-block;
        line-height: 0;
    }
    
    /* Empty state image styling for mobile */
    img[src*="empty-states/Album.svg"] {
        width: 94px !important;
        height: 93px !important;
        object-fit: contain !important;
        margin:10px;

    }
}

/* Note: Mobile modal styling now unified in the main mobile media query above */

/* Enhanced modal download button - matching maximize button styling */
.modal-download-btn {
    position: absolute !important;
    top: 16px !important;
    right: 16px !important;
    background: transparent !important; /* Start with transparent background like maximize button */
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    padding: 8px !important; /* Match maximize button default padding */
    border-radius: 8px !important; /* Match maximize button border radius */
    cursor: pointer !important;
    transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1) !important; /* Premium transition matching maximize button */
    backdrop-filter: none !important; /* Start without blur */
    -webkit-backdrop-filter: none !important;
    z-index: 50 !important;
    box-shadow: none !important; /* Start without shadow */
    /* Enhanced opacity behavior matching maximize button */
    opacity: 0.8 !important; /* Match maximize button default opacity */
    transform: none !important;
    will-change: transform, opacity, background, backdrop-filter !important;
    pointer-events: auto !important;
}

/* Enhanced hover and focus states matching maximize button */
.modal-download-btn:hover,
.modal-download-btn:focus-visible {
    opacity: 1 !important; /* Full opacity on hover/focus */
    backdrop-filter: blur(6px) !important; /* Glassy background effect */
    -webkit-backdrop-filter: blur(6px) !important;
    background: rgba(20, 20, 30, 0.25) !important; /* Subtle dark glassy background */
    border-color: rgba(255, 255, 255, 0.2) !important; /* Slightly more prominent border */
    color: rgba(255, 255, 255, 1) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important; /* Subtle shadow on hover */
}

.modal-download-btn:focus-visible {
    outline: 2px solid #a78bfa !important;
    outline-offset: 2px !important;
}

.modal-download-btn:active {
    opacity: 0.6 !important; /* Match maximize button active state */
    transform: scale(0.95) !important;
}

/* Enhanced tooltip for download button */
.modal-download-btn::after {
    content: 'Download';
    position: absolute;
    bottom: -32px; /* Increased from -29px by 10% (-29 * 1.1 = -31.9 ≈ -32px) */
    right: 0;
    background: rgba(17, 24, 39, 0.95);
    color: white;
    padding: 7px 11px; /* Increased from 6px 10px by 10% (6*1.1=6.6≈7px, 10*1.1=11px) */
    border-radius: 7px; /* Increased from 6px by 10% (6 * 1.1 = 6.6 ≈ 7px) */
    font-size: 11px; /* Increased from 10px by 10% (10 * 1.1 = 11px) */
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all 0.2s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 3px 11px rgba(0, 0, 0, 0.3); /* Increased from 3px 10px by 10% (3px stays, 10*1.1=11px) */
    z-index: 1000;
}

.modal-download-btn:hover::after {
    opacity: 1;
    transform: translateY(-2px);
}

/* Animations for preview modal */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes modalSlideOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.95) translateY(20px);
    }
}

@keyframes backdropFadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Tablet responsiveness */
@media (max-width: 1024px) {
    .modal-title-bar {
        padding: 8px 16px; /* Reduced from 10px by 15% (10 * 0.85 = 8.5 ≈ 8px) */
        min-height: 34px; /* Reduced from 40px by 15% (40 * 0.85 = 34px) */
    }

    .traffic-light {
        width: 14px;
        height: 14px;
    }

    /* Close button responsive styling handled by Tailwind classes */

    .modal-download-btn {
        padding: 10px !important; /* Match tablet maximize button padding */
        top: 12px !important;
        right: 12px !important;
    }

    .thumbnail-maximize-btn {
        top: 10px !important;
        right: 10px !important;
        padding: 7px !important;
    }

    .password-toggle-btn {
        right: 8px !important;
        padding: 2px !important;
    }

    .modal-close-btn-fixed {
        top: 8px !important;
        right: 8px !important;
        padding: 4px !important;
    }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .thumbnail-preview-modal-backdrop {
        padding: 10px;
    }

    .thumbnail-preview-modal-content {
        width: fit-content;
        height: fit-content;
        /* Responsive sizing with larger canvas for better viewing */
        max-width: none;
        max-height: none;
    }

    .modal-title-bar {
        padding: 7px 12px; /* Reduced from 8px by 15% (8 * 0.85 = 6.8 ≈ 7px) */
        min-height: 31px; /* Reduced from 36px by 15% (36 * 0.85 = 30.6 ≈ 31px) */
    }

    .traffic-lights {
        gap: 5px; /* Slightly reduced gap for better proportions */
    }

    .traffic-light {
        width: 13px; /* Reduced from 14px by 10% (14 * 0.90 = 12.6 ≈ 13px) */
        height: 13px; /* Reduced from 14px by 10% (14 * 0.90 = 12.6 ≈ 13px) */
    }

    /* Close button mobile styling handled by Tailwind classes */

    .modal-download-btn {
        padding: 12px !important; /* Increased from 8px by 50% for better mobile touch targets */
        top: 8px !important;
        right: 8px !important;
    }
    
    .modal-download-btn .iconify {
        font-size: 24px !important; /* Increased from 20px by 20% for better mobile visibility */
    }

    .modal-download-btn::after {
        display: none;
    }

    .thumbnail-maximize-container {
        width: 80px !important;
        height: 60px !important;
    }
    
    .gradient-feather {
        height: 50px !important;
    }

    .thumbnail-maximize-btn {
        top: 8px !important;
        right: 8px !important;
        padding: 10px !important; /* Increased from 6px by 25% (6 + 4 = 10) */
    }

    .thumbnail-maximize-btn .iconify {
        font-size: 24px !important; /* Increased from 18px by 25% (18 + 6 = 24) */
    }

    .password-toggle-btn {
        right: 10px !important;
        padding: 3px !important;
    }

    .modal-close-btn-fixed {
        top: 10px !important;
        right: 10px !important;
        padding: 6px !important;
    }
}

/* Extra small mobile */
@media (max-width: 480px) {
    .modal-title-bar {
        padding: 5px 10px; /* Reduced from 6px by 15% (6 * 0.85 = 5.1 ≈ 5px) */
        min-height: 27px; /* Reduced from 32px by 15% (32 * 0.85 = 27.2 ≈ 27px) */
    }

    .traffic-light {
        width: 14px; /* Reduced from 15px by 10% (15 * 0.90 = 13.5 ≈ 14px) */
        height: 14px; /* Reduced from 15px by 10% (15 * 0.90 = 13.5 ≈ 14px) */
    }

    /* Close button extra small mobile styling handled by Tailwind classes */

    .thumbnail-preview-modal-backdrop {
        padding: 5px;
    }
    
    .thumbnail-maximize-container {
        width: 70px !important;
        height: 50px !important;
    }
    
    .gradient-feather {
        height: 40px !important;
    }
    
    .thumbnail-maximize-btn {
        top: 6px !important;
        right: 6px !important;
        padding: 8px !important; /* Increased from 5px by 25% (5 + 3 = 8) */
    }
    
    .thumbnail-maximize-btn .iconify {
        font-size: 20px !important; /* Increased from 16px by 25% (16 + 4 = 20) */
    }
    
    .modal-download-btn {
        padding: 10px !important; /* Slightly smaller for extra small screens */
        top: 6px !important;
        right: 6px !important;
    }
    
    .modal-download-btn .iconify {
        font-size: 20px !important; /* Match extra small mobile maximize button icon size */
    }
}

@media (max-width: 768px) and (orientation: portrait) {
  .form-content-container,
  .flex.flex-col.justify-center.items-center {
    justify-content: inherit !important;
    gap: .5rem;
  }
}