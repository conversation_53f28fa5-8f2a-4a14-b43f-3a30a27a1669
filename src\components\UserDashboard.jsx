import React, { useState, useEffect, useRef } from 'react'
import { Icon } from '@iconify/react'
import { downloadThumbnailAt1280x720 } from '../utils/imageUtils.js'
import { getHistory, deleteHistoryItem } from '../utils/supabaseHistoryManager.js'
import SearchableCountrySelect from './ui/SearchableCountrySelect.jsx'

// Import dashboard-specific styles
import '../styles/dashboard.css';
import '../styles/dashboard-premium-transitions.css'; // Add new premium transitions
import '../styles/confirmation-modal.css';
import '../styles/generation-details-modal.css';
import { ConfirmationModal } from './ui/ConfirmationModal.jsx';
import { GenerationDetailsModal } from './ui/GenerationDetailsModal.jsx';
import { ThumbnailPreviewModal } from './ui/ThumbnailPreviewModal.jsx';
import { PasswordChangeModal } from './ui/PasswordChangeModal.jsx';
import { AvatarUploadModal } from './ui/AvatarUploadModal.jsx';
import { safeSetItem, safeGetItem, safeRemoveItem, getStorageStats } from '../utils/localStorageManager.js';
import { authAPI, supabase } from '../utils/supabase.mjs';
import { PasswordRateLimiter, getRateLimitMessage } from '../utils/passwordRateLimit.js';
import { createClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '../config/supabase.mjs';
import { rememberMeAuth } from '../utils/auth.js';
import {
    generateUniqueUsername,
    validateUsername as validateUsernameUtil,
    getUserUsername,
    needsProfileInitialization,
    initializeUserProfile
} from '../utils/userProfileUtils.js';

// Initialize Supabase client
const supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

export const UserDashboard = ({ user, onExitDashboard = null, onUpdateUser = null, onOpenSubscriptionModal = null, isClosing = false }) => {
    const [activeTab, setActiveTab] = useState('overview');
    const [isEditing, setIsEditing] = useState(false);
    const [editableUser, setEditableUser] = useState({
        fullName: '',
        email: '',
        plan: '',
        credits: 0,
        maxCredits: 0,
        nextBilling: '',
        lastLogin: '',
        memberSince: ''
    });
    const [previousTab, setPreviousTab] = useState('overview');
    const [isTabTransitioning, setIsTabTransitioning] = useState(false);

    // Generation History filter states
    const [sortOrder, setSortOrder] = useState('newest'); // 'newest' or 'oldest'
    const [qualityFilter, setQualityFilter] = useState('all'); // 'all', 'hd', 'medium', 'low'

    // Confirmation modal state
    const [confirmModal, setConfirmModal] = useState({
        isOpen: false,
        title: '',
        message: '',
        onConfirm: null,
        itemToDelete: null
    });

    // Generation details modal state
    const [detailsModal, setDetailsModal] = useState({
        isOpen: false,
        thumbnail: null,
        prompt: '',
        settings: {}
    });

    // Thumbnail preview modal state
    const [previewModal, setPreviewModal] = useState({
        isOpen: false,
        thumbnail: null,
        title: null,
        itemId: null
    });

    // Mobile portrait detection state
    const [isMobilePortrait, setIsMobilePortrait] = useState(false);

    // Password change modal state
    const [passwordModal, setPasswordModal] = useState({
        isOpen: false,
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
        showCurrentPassword: false,
        showNewPassword: false,
        showConfirmPassword: false,
        errors: {},
        isLoading: false,
        successMessage: ''
    });

    // Password rate limiting state
    const [passwordRateLimit, setPasswordRateLimit] = useState({
        canChange: true,
        eligibility: null,
        message: null,
        isChecking: false
    });

    // Debug toggle state for testing empty states
    const [debugEmptyState, setDebugEmptyState] = useState(false);

    // State for avatar upload functionality
    const [avatarFile, setAvatarFile] = useState(null);
    const [avatarPreview, setAvatarPreview] = useState(null); // Used for the upload modal's internal preview
    const [optimisticAvatarUrl, setOptimisticAvatarUrl] = useState(null); // Used for displaying the avatar in the dashboard
    const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
    const [uploadModal, setUploadModal] = useState({ isOpen: false });

    const [currentUser, setCurrentUser] = useState(user);

    const handleUpdateUser = (updatedUser) => {
        setCurrentUser(updatedUser);           // this makes the avatar stick
        window.dispatchEvent(
            new CustomEvent('userAvatarUpdated', { detail: { newUser: updatedUser } })
        );
    };

    // Listen for avatar updates from other components (like main app)
    useEffect(() => {
        const handleAvatarUpdate = (event) => {
            const { newUser } = event.detail;
            if (newUser && newUser.id === user?.id) {
                setCurrentUser(newUser);
                // Clear optimistic state since we have fresh data
                setOptimisticAvatarUrl(null);
            }
        };

        window.addEventListener('userAvatarUpdated', handleAvatarUpdate);

        return () => {
            window.removeEventListener('userAvatarUpdated', handleAvatarUpdate);
        };
    }, [user?.id]);

    // NEW: Subscription plan overlay state
    const [subscriptionOverlay, setSubscriptionOverlay] = useState({
        isOpen: false,
        isTransitioning: false,
        selectedPlan: null
    });

    // NEW: Subscription pricing modal state (replacing overlay)
    const [isPricingModalOpen, setIsPricingModalOpen] = useState(false);
    const [isModalClosing, setIsModalClosing] = useState(false);

    // NEW: Modal transition states for smooth animations
    const [modalTransition, setModalTransition] = useState({
        isVisible: false,
        isAnimating: false,
        animationType: null // 'fadeIn' or 'fadeOut'
    });

    // Local toast state for immediate dashboard feedback
    const [localToast, setLocalToast] = useState({
        isVisible: false,
        message: '',
        type: 'success'
    });

    // NEW: Billing edit state
    const [billingEditMode, setBillingEditMode] = useState(false);
    const [billingFormData, setBillingFormData] = useState({
        fullName: '',
        email: '',
        phone: '',
        country: '',
        address: '',
        postalCode: ''
    });
    const [billingErrors, setBillingErrors] = useState({});
    const [isSavingBilling, setIsSavingBilling] = useState(false);

    // Countries list for dropdown
    const countries = [
        'United States', 'Canada', 'United Kingdom', 'Australia', 'Germany',
        'France', 'Japan', 'Brazil', 'India', 'Mexico', 'Spain', 'Italy',
        'Netherlands', 'Sweden', 'Norway', 'Denmark', 'Belgium', 'Switzerland'
    ];

    // Initialize/sync avatar URL from the user prop
    useEffect(() => {
        setOptimisticAvatarUrl(currentUser?.user_metadata?.avatar_url || null);
    }, [currentUser]);

    // ================= SUBSCRIPTION PLAN OVERLAY FUNCTIONS =================

    // Pricing plans configuration
    const pricingPlans = [
        {
            id: 'free',
            name: 'Free Plan',
            price: '$0',
            period: '/month',
            description: 'Perfect for getting started',
            features: [
                '5 generations per month',
                'Basic templates',
                'Standard quality',
                'Community support'
            ],
            buttonText: 'Current Plan',
            isCurrentPlan: editableUser.plan === 'free',
            highlight: false,
            color: '#10B981',
            badgeText: 'Current Plan',
            badgeColor: 'success'
        },
        {
            id: 'basic',
            name: 'Basic Plan',
            price: '$19',
            period: '/month',
            description: 'Enhanced features for creators',
            features: [
                'Unlimited generations',
                'Premium templates',
                'Advanced text styling',
                'Priority support',
                'HD quality exports'
            ],
            buttonText: editableUser.plan === 'basic' ? 'Current Plan' : 'Upgrade to Basic',
            isCurrentPlan: editableUser.plan === 'basic',
            highlight: true,
            color: '#8B5CF6',
            mostPopular: true,
            badgeText: 'Most Popular',
            badgeColor: 'secondary'
        },
        {
            id: 'pro',
            name: 'Pro Plan',
            price: '$49',
            period: '/month',
            description: 'Complete toolkit for professionals',
            features: [
                'Everything in Basic',
                'AI recommendations',
                'Batch generation',
                'Custom presets',
                'White-label options',
                'Priority processing'
            ],
            buttonText: editableUser.plan === 'pro' ? 'Current Plan' : 'Upgrade to Pro',
            isCurrentPlan: editableUser.plan === 'pro',
            highlight: false,
            color: '#F59E0B'
        }
    ];

    // Handle opening subscription overlay
    const handleOpenSubscriptionOverlay = (planId = 'pro') => {
        const selectedPlan = pricingPlans.find(plan => plan.id === planId) || pricingPlans[1];

        setSubscriptionOverlay({
            isOpen: true,
            isTransitioning: false,
            selectedPlan: selectedPlan
        });
    };

    // Handle closing subscription overlay with smooth transition
    const handleCloseSubscriptionOverlay = () => {
        setSubscriptionOverlay(prev => ({
            ...prev,
            isTransitioning: true
        }));

        // Allow transition to complete before hiding
        setTimeout(() => {
            setSubscriptionOverlay({
                isOpen: false,
                isTransitioning: false,
                selectedPlan: null
            });
        }, 300); // Match CSS transition duration
    };

    // Handle plan selection
    const handlePlanSelect = (planId) => {
        if (planId === editableUser.plan) return; // Already on this plan

        // Find the selected plan and open subscription modal
        const selectedPlan = pricingPlans.find(plan => plan.id === planId);
        if (selectedPlan && !selectedPlan.isCurrentPlan && onOpenSubscriptionModal) {
            onOpenSubscriptionModal(planId);
            handleCloseSubscriptionOverlay();
        }
    };

    // Handle opening subscription pricing modal
    const handleOpenPricingModal = (planId = 'pro') => {
        // Start the opening sequence
        setIsPricingModalOpen(true);
        setModalTransition({
            isVisible: true,
            isAnimating: true,
            animationType: 'fadeIn'
        });

        // Complete fade-in animation
        setTimeout(() => {
            setModalTransition(prev => ({
                ...prev,
                isAnimating: false
            }));
        }, 300); // Match CSS transition duration
    };

    // Handle closing subscription pricing modal with smooth fade-out
    const handleClosePricingModal = () => {
        // Start the closing sequence
        setModalTransition({
            isVisible: true,
            isAnimating: true,
            animationType: 'fadeOut'
        });

        // Complete fade-out animation and hide modal
        setTimeout(() => {
            setIsPricingModalOpen(false);
            setModalTransition({
                isVisible: false,
                isAnimating: false,
                animationType: null
            });
        }, 300); // Match CSS transition duration
    };

    // Handle pricing plan selection
    const handlePricingPlanSelect = (planId) => {
        if (planId === editableUser.plan) return; // Already on this plan

        // Find the selected plan and open subscription modal
        const selectedPlan = pricingPlans.find(plan => plan.id === planId);
        if (selectedPlan && !selectedPlan.isCurrentPlan && onOpenSubscriptionModal) {
            onOpenSubscriptionModal(planId);
            setIsPricingModalOpen(false);
        }
    };

    // Check password change eligibility on mount and user change
    useEffect(() => {
        const checkPasswordEligibility = async () => {
            if (!user) return;

            setPasswordRateLimit(prev => ({ ...prev, isChecking: true }));

            try {
                const rateLimiter = new PasswordRateLimiter(supabaseClient);
                const eligibility = await rateLimiter.checkPasswordChangeEligibility(user.id);
                const message = getRateLimitMessage(eligibility);

                setPasswordRateLimit({
                    canChange: eligibility.canChange,
                    eligibility,
                    message,
                    isChecking: false
                });
            } catch (error) {
                console.error('Error checking password eligibility:', error);
                // Fail open - allow password change on error
                setPasswordRateLimit({
                    canChange: true,
                    eligibility: null,
                    message: null,
                    isChecking: false
                });
            }
        };

        checkPasswordEligibility();
    }, [user]);

    // Detect mobile portrait mode
    useEffect(() => {
        const checkMobilePortrait = () => {
            const isMobile = window.innerWidth <= 768;
            const isPortrait = window.matchMedia('(orientation: portrait)').matches;
            setIsMobilePortrait(isMobile && isPortrait);
        };

        checkMobilePortrait();
        window.addEventListener('resize', checkMobilePortrait);
        window.addEventListener('orientationchange', checkMobilePortrait);

        return () => {
            window.removeEventListener('resize', checkMobilePortrait);
            window.removeEventListener('orientationchange', checkMobilePortrait);
        };
    }, []);

    // Initialize editable user data
    useEffect(() => {
        if (currentUser) {
            const getLastLogin = () => {
                if (currentUser.last_sign_in_at) {
                    return new Date(currentUser.last_sign_in_at).toLocaleString();
                }
                return new Date().toLocaleString();
            };

            const getNextBilling = () => {
                const nextMonth = new Date();
                nextMonth.setMonth(nextMonth.getMonth() + 1);
                return nextMonth.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            };

            // Get or generate username using utility function
            const username = getUserUsername(currentUser);

            setEditableUser({
                fullName: currentUser.user_metadata?.full_name || currentUser.email?.split('@')[0] || 'User',
                username: username,
                email: currentUser.email || '<EMAIL>',
                plan: currentUser.app_metadata?.plan || 'free',
                credits: currentUser.app_metadata?.credits || 750,
                maxCredits: currentUser.app_metadata?.max_credits || 1000,
                nextBilling: currentUser.app_metadata?.next_billing || getNextBilling(),
                lastLogin: getLastLogin(),
                memberSince: currentUser.created_at ? new Date(currentUser.created_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                }) : new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                })
            });
        }
    }, [currentUser]);

    // Smooth tab switching animation
    const handleTabChange = (newTab) => {
        if (newTab !== activeTab) {
            setIsTabTransitioning(true);
            setPreviousTab(activeTab);

            setTimeout(() => {
                setActiveTab(newTab);
                setIsTabTransitioning(false);
            }, 150); // Quick transition
        }
    };

    const [generationHistory, setGenerationHistory] = useState([]);

    // Load generation history on component mount and when user changes
    useEffect(() => {
        const loadHistory = async () => {
            try {
                // First try to get from Supabase
                const supabaseHistory = await getHistory();
                if (supabaseHistory && supabaseHistory.length > 0) {
                    // Transform Supabase data to match expected format
                    const transformedHistory = supabaseHistory.map(item => ({
                        id: item.id,
                        timestamp: item.created_at,
                        date: item.created_at,
                        title: item.title,
                        prompt: item.prompt,
                        finalPrompt: item.final_prompt,
                        thumbnail: item.thumbnail_url, // Use compressed thumbnail for display
                        fullImageUrl: item.full_image_url, // Store full image URL for downloads
                        quality: item.quality,
                        credits: item.credits,
                        metadata: item.metadata || {}
                    }));
                    setGenerationHistory(transformedHistory);
                    console.log(`📦 Loaded ${transformedHistory.length} items from Supabase`);
                    return;
                }

                console.log('⚠️ No Supabase history found, falling back to localStorage');

                // Fallback: try to get from user metadata
                if (user?.app_metadata?.generation_history) {
                    setGenerationHistory(user.app_metadata.generation_history);
                    return;
                }

                // Fallback to localStorage for local storage of generations
                const stored = localStorage.getItem('user_generation_history');
                if (stored) {
                    setGenerationHistory(JSON.parse(stored));
                    return;
                }

                // Return empty array if no data found
                setGenerationHistory([]);
            } catch (error) {
                console.error('🚨 Error loading generation history:', error);
                // Fallback to localStorage on error
                try {
                    const stored = localStorage.getItem('user_generation_history');
                    if (stored) {
                        setGenerationHistory(JSON.parse(stored));
                    } else {
                        setGenerationHistory([]);
                    }
                } catch (fallbackError) {
                    console.error('Fallback to localStorage also failed:', fallbackError);
                    setGenerationHistory([]);
                }
            }
        };

        loadHistory();
    }, [user]);

    // Close dropdowns when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            // If click is not on dropdown or button, close all dropdowns
            if (!event.target.closest('.show-more-container') && !event.target.closest('.dropdown-menu')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    if (menu.style.display === 'flex') {
                        menu.style.display = 'none';
                    }
                });
            }
        };

        document.addEventListener('click', handleClickOutside);
        return () => {
            document.removeEventListener('click', handleClickOutside);
        };
    }, []);

    // Generation History helper functions
    const handleSortToggle = () => {
        setSortOrder(prev => prev === 'newest' ? 'oldest' : 'newest');
    };

    const handleQualityFilterToggle = () => {
        const qualityOptions = ['all', 'hd', 'medium', 'low'];
        const currentIndex = qualityOptions.indexOf(qualityFilter);
        const nextIndex = (currentIndex + 1) % qualityOptions.length;
        setQualityFilter(qualityOptions[nextIndex]);
    };

    const getQualityFilterLabel = () => {
        switch (qualityFilter) {
            case 'hd': return 'HD Only';
            case 'medium': return 'Medium Only';
            case 'low': return 'Low Only';
            default: return 'All Qualities';
        }
    };

    // Delete generation function
    const handleDeleteGeneration = (itemToDelete) => {
        console.log('Delete clicked for item:', itemToDelete);
        setConfirmModal({
            isOpen: true,
            title: 'Delete Generation',
            message: 'Are you sure you want to delete this generation? This action cannot be undone.',
            onConfirm: () => performDeleteGeneration(itemToDelete),
            itemToDelete: itemToDelete
        });
        console.log('Modal state set to:', { isOpen: true });
    };

    // Perform actual deletion
    const performDeleteGeneration = async (itemToDelete) => {
        try {
            // Remove from current state immediately for better UX
            const updatedHistory = generationHistory.filter(h =>
                (h.id || h.timestamp) !== (itemToDelete.id || itemToDelete.timestamp)
            );
            setGenerationHistory(updatedHistory);

            // Try to delete from Supabase first (if it has the required URLs)
            if (itemToDelete.id && itemToDelete.fullImageUrl && itemToDelete.thumbnail) {
                try {
                    const deleted = await deleteHistoryItem(
                        itemToDelete.id,
                        itemToDelete.fullImageUrl,
                        itemToDelete.thumbnail
                    );
                    if (deleted) {
                        console.log('🗑️ Successfully deleted from Supabase');
                    } else {
                        console.warn('⚠️ Failed to delete from Supabase, but continuing...');
                    }
                } catch (supabaseError) {
                    console.warn('⚠️ Supabase deletion failed:', supabaseError.message);
                    // Continue with localStorage deletion even if Supabase fails
                }
            }

            // Update localStorage (fallback and for older entries)
            localStorage.setItem('user_generation_history', JSON.stringify(updatedHistory));

            // Close the modal
            closeConfirmModal();

            console.log('✅ Generation deleted successfully');
        } catch (error) {
            console.error('🚨 Error deleting generation:', error);
            // Reload the history in case of error
            try {
                const stored = localStorage.getItem('user_generation_history');
                if (stored) {
                    setGenerationHistory(JSON.parse(stored));
                }
            } catch (fallbackError) {
                console.error('Failed to reload from localStorage:', fallbackError);
                // If all else fails, reload from Supabase
                const loadHistory = async () => {
                    try {
                        const supabaseHistory = await getHistory();
                        if (supabaseHistory) {
                            const transformedHistory = supabaseHistory.map(item => ({
                                id: item.id,
                                timestamp: item.created_at,
                                date: item.created_at,
                                title: item.title,
                                prompt: item.prompt,
                                finalPrompt: item.final_prompt,
                                thumbnail: item.thumbnail_url,
                                fullImageUrl: item.full_image_url,
                                quality: item.quality,
                                credits: item.credits,
                                metadata: item.metadata || {}
                            }));
                            setGenerationHistory(transformedHistory);
                        }
                    } catch (reloadError) {
                        console.error('Failed to reload from Supabase:', reloadError);
                    }
                };
                loadHistory();
            }
            // Close the modal even on error
            closeConfirmModal();
        }
    };

    // Close confirmation modal
    const closeConfirmModal = () => {
        setConfirmModal({
            isOpen: false,
            title: '',
            message: '',
            onConfirm: null,
            itemToDelete: null
        });
    };

    // Handle delete account confirmation
    const handleDeleteAccount = () => {
        setConfirmModal({
            isOpen: true,
            title: 'Delete Account',
            message: 'This will permanently delete your account and all data. This cannot be undone. Continue?',
            onConfirm: performDeleteAccount,
            itemToDelete: null
        });
    };

    // Perform actual account deletion
    const performDeleteAccount = () => {
        try {
            // Here you would typically call your backend API to delete the account
            console.log('Account deletion initiated for user:', user?.email);

            // Clear all local data
            localStorage.removeItem('user_generation_history');
            localStorage.clear();

            // Close the modal
            closeConfirmModal();

            // Redirect or sign out user
            if (onExitDashboard) {
                onExitDashboard();
            }

            // In a real app, you would:
            // 1. Call your backend API to delete the account
            // 2. Handle the response
            // 3. Sign out the user
            // 4. Redirect to a confirmation page

            console.log('Account deletion process completed');
        } catch (error) {
            console.error('Error deleting account:', error);
            // In a real app, you would show an error message to the user
            closeConfirmModal();
        }
    };

    // Handle password change
    const handlePasswordChange = async ({ currentPassword, newPassword }) => {
        try {
            console.log('Password change requested for user:', user?.email);

            // Check rate limiting before proceeding
            if (!passwordRateLimit.canChange) {
                throw new Error(passwordRateLimit.message?.message || 'Password change not allowed at this time.');
            }

            // For password changes in Supabase, we need to verify the current password first
            // Store current session to restore later if needed
            const { data: currentSession } = await supabase.auth.getSession();

            if (!currentSession?.session?.access_token) {
                throw new Error("No active session found. Please log in again.");
            }

            // CRITICAL: Verify current password using a separate client instance to avoid session conflicts
            console.log("🔐 Verifying current password for user:", user.email);

            // Create a temporary client for verification only
            const tempClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

            const { data: verifyData, error: verifyError } = await tempClient.auth.signInWithPassword({
                email: user.email,
                password: currentPassword,
            });

            if (verifyError) {
                console.error("🚨 SECURITY: Current password verification FAILED:", verifyError.message);
                // NEVER proceed with password change if verification fails
                throw new Error("Current password is incorrect. Please verify your current password and try again.");
            }

            // Double-check that the verification actually succeeded
            if (!verifyData?.user || verifyData.user.id !== user.id) {
                console.error("🚨 SECURITY: Password verification returned unexpected user data");
                throw new Error("Authentication verification failed. Please try again.");
            }

            console.log("✅ Current password verification successful");            // Use the updateUser method which works for authenticated users
            const { data: updateData, error: updateError } = await supabase.auth.updateUser({
                password: newPassword
            });

            if (updateError) {
                console.error('Password update failed:', updateError);
                throw new Error(updateError.message || 'Failed to update password. Please try again.');
            }

            // Record the successful password change for rate limiting
            try {
                const rateLimiter = new PasswordRateLimiter(supabaseClient);
                await rateLimiter.recordPasswordChange(user.id, {
                    ip: 'browser', // Could be enhanced to get real IP
                    userAgent: navigator.userAgent,
                    source: 'dashboard'
                });

                // Refresh eligibility status
                const newEligibility = await rateLimiter.checkPasswordChangeEligibility(user.id);
                const newMessage = getRateLimitMessage(newEligibility);

                setPasswordRateLimit({
                    canChange: newEligibility.canChange,
                    eligibility: newEligibility,
                    message: newMessage,
                    isChecking: false
                });
            } catch (recordError) {
                console.error('Error recording password change:', recordError);
                // Don't fail the password change if recording fails
            }

            console.log('Password changed successfully');

            // Restore the original session to ensure the user remains logged in seamlessly
            if (currentSession?.session) {
                await supabase.auth.setSession(currentSession.session);
                console.log('✅ User session restored after password change.');
            }

            return true;
        } catch (error) {
            console.error('Error changing password:', error);
            // Re-throw with a user-friendly message if it's not already one
            if (error.message.includes('Current password is incorrect') ||
                error.message.includes('Failed to update password') ||
                error.message.includes('Password change not allowed')) {
                throw error;
            }
            throw new Error('An unexpected error occurred. Please try again.');
        }
    };

    // Filter and sort generation history
    const filteredAndSortedHistory = generationHistory
        .filter(item => {
            if (qualityFilter === 'all') return true;
            const itemQuality = (item.quality || 'Medium').toLowerCase();
            return itemQuality === qualityFilter;
        })
        .sort((a, b) => {
            const dateA = new Date(a.date || a.timestamp);
            const dateB = new Date(b.date || b.timestamp);
            return sortOrder === 'newest' ? dateB - dateA : dateA - dateB;
        });

    const tabs = [
        {
            id: 'overview',
            label: 'Overview',
            icon: 'solar:widget-2-bold-duotone'
        },
        {
            id: 'account',
            label: 'Account',
            icon: 'solar:user-circle-bold-duotone'
        },
        {
            id: 'billing',
            label: isMobilePortrait ? 'Billing' : 'Billing & Subscription',
            icon: 'solar:card-bold-duotone'
        },
        {
            id: 'history',
            label: isMobilePortrait ? 'History' : 'Generation History',
            icon: 'solar:history-bold-duotone'
        }
    ];

    const validateUsername = (username) => {
        const validation = validateUsernameUtil(username);
        return validation.isValid ? null : validation.error;
    };

    const handleSaveProfile = async () => {
        try {
            // Validate username
            const usernameError = validateUsername(editableUser.username);
            if (usernameError) {
                showLocalToast(usernameError, 'error');
                return;
            }

            // Validate full name
            if (!editableUser.fullName || editableUser.fullName.trim().length < 1) {
                showLocalToast('Full name is required', 'error');
                return;
            }

            // Update user metadata in Supabase
            const { error } = await supabaseClient.auth.updateUser({
                data: {
                    full_name: editableUser.fullName.trim(),
                    username: editableUser.username.toLowerCase(),
                    // Preserve existing avatar data
                    avatar_url: user.user_metadata?.avatar_url,
                    avatar_storage: user.user_metadata?.avatar_storage
                }
            });

            if (error) {
                console.error('Failed to update profile:', error);
                showLocalToast('Failed to update profile. Please try again.', 'error');
                return;
            }

            // Update local state
            if (onUpdateUser) {
                const updatedUser = {
                    ...user,
                    user_metadata: {
                        ...user.user_metadata,
                        full_name: editableUser.fullName.trim(),
                        username: editableUser.username.toLowerCase()
                    }
                };
                onUpdateUser(updatedUser);
            }

            showLocalToast('Profile updated successfully!', 'success');
            setIsEditing(false);
        } catch (error) {
            console.error('Error updating profile:', error);
            showLocalToast('An error occurred while updating your profile.', 'error');
        }
    };

    const handleCancelEdit = () => {
        // Reset to original user data
        const getLastLogin = () => {
            if (user.last_sign_in_at) {
                return new Date(user.last_sign_in_at).toLocaleString();
            }
            return new Date().toLocaleString();
        };

        const getNextBilling = () => {
            const nextMonth = new Date();
            nextMonth.setMonth(nextMonth.getMonth() + 1);
            return nextMonth.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        };

        // Get or generate username using utility function
        const username = getUserUsername(user);

        setEditableUser({
            fullName: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
            username: username,
            email: user.email || '<EMAIL>',
            plan: user.app_metadata?.plan || 'free',
            credits: user.app_metadata?.credits || 750,
            maxCredits: user.app_metadata?.max_credits || 1000,
            nextBilling: user.app_metadata?.next_billing || getNextBilling(),
            lastLogin: getLastLogin(),
            memberSince: user.created_at ? new Date(user.created_at).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            }) : new Date().toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            })
        });
        setIsEditing(false);
    };

    // Enhanced Avatar upload handler with fallback for missing storage bucket
    const handleAvatarUpload = async (file) => {
        try {
            setIsUploadingAvatar(true);

            if (!user?.id) {
                throw new Error('User not authenticated');
            }

            // First, try to upload to Supabase Storage
            try {
                // 1. Upload file to Supabase Storage
                const fileExt = file.name.split('.').pop();
                const fileName = `avatar-${user.id}-${Date.now()}.${fileExt}`;
                const filePath = `avatars/${fileName}`;

                const { data: uploadData, error: uploadError } = await supabaseClient.storage
                    .from('user-avatars')
                    .upload(filePath, file, {
                        cacheControl: '3600',
                        upsert: true
                    });

                if (uploadError) {
                    // Check for common storage errors
                    if (uploadError.message?.includes('Bucket not found') || uploadError.message?.includes('bucket_not_found')) {
                        console.warn('❌ Supabase Storage bucket "user-avatars" not found. Using local fallback.');
                        throw new Error('BUCKET_NOT_FOUND');
                    }
                    console.error('Supabase storage upload error:', uploadError);
                    throw new Error(`Supabase upload failed: ${uploadError.message}`);
                }

                // 2. Get the public URL
                const { data: urlData } = supabaseClient.storage
                    .from('user-avatars')
                    .getPublicUrl(filePath);

                if (!urlData?.publicUrl) {
                    throw new Error('Failed to get public URL from Supabase');
                }

                const avatarUrl = urlData.publicUrl;

                // 3. Update user metadata with avatar URL and source
                const { error: updateError } = await supabaseClient.auth.updateUser({
                    data: {
                        avatar_url: avatarUrl,
                        avatar_source: 'supabase', // Add source tracking
                        avatar_path: filePath // Store the storage path
                    }
                });

                if (updateError) {
                    console.error('User update error:', updateError);
                    throw new Error(`Failed to update user: ${updateError.message}`);
                }

                // 4. Refresh the session to get the updated user metadata
                const { data: { session }, error: sessionError } = await supabaseClient.auth.getSession();
                if (sessionError) {
                    console.warn('Could not refresh session:', sessionError);
                }

                // 5. Update local state with cache-busted URL and propagate to parent
                const cacheBustedUrl = `${avatarUrl}?t=${new Date().getTime()}`;
                setOptimisticAvatarUrl(cacheBustedUrl);
                setAvatarFile(file);

                // 6. Notify parent component about the user update with refreshed data
                const freshUser = session.user;
                if (onUpdateUser) {
                    onUpdateUser(freshUser);
                }

                // 7. Dispatch global event for other components
                window.dispatchEvent(
                    new CustomEvent('userAvatarUpdated', {
                        detail: {
                            newUser: freshUser,
                            avatarUrl: cacheBustedUrl,
                            avatarSource: 'supabase',
                            avatarPath: filePath
                        }
                    })
                );

                console.log('✅ Avatar uploaded to Supabase successfully:', avatarUrl);

            } catch (supabaseError) {
                const isBucketError = supabaseError.message === 'BUCKET_NOT_FOUND';

                if (isBucketError) {
                    console.warn('📦 Supabase Storage bucket not available, using local fallback');
                } else {
                    console.warn('⚠️ Supabase storage error, using local fallback:', supabaseError.message);
                }

                // Fallback: Use local data URL (base64) for demo purposes
                const reader = new FileReader();
                const avatarDataUrl = await new Promise((resolve, reject) => {
                    reader.onload = (e) => resolve(e.target.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                });

                // Store in localStorage as fallback using safe storage manager
                const avatarKey = `avatar_${user.id}_${Date.now()}`;
                const storageResult = safeSetItem(avatarKey, avatarDataUrl);

                if (!storageResult.success) {
                    throw new Error(storageResult.error || 'Failed to store avatar data');
                }

                if (storageResult.cleanupPerformed) {
                    console.log('✅ Avatar stored successfully after localStorage cleanup');
                }

                // Remove old avatar for this user
                const oldAvatarKey = `avatar_${user.id}`;
                safeRemoveItem(oldAvatarKey);

                // Update local state
                setOptimisticAvatarUrl(avatarDataUrl);
                setAvatarFile(file);

                // For demo purposes, we'll store the data URL in user metadata
                // In production, this would be the Supabase URL
                try {
                    const { error: updateError } = await supabaseClient.auth.updateUser({
                        data: {
                            avatar_url: avatarDataUrl,
                            avatar_source: 'local' // Flag to indicate local storage
                        }
                    });

                    if (updateError) {
                        console.warn('Could not update user metadata, using local state only');
                    }
                } catch (metadataError) {
                    console.warn('User metadata update failed, using local state only');
                }

                // 5. Refresh the session to get any updated user metadata
                const { data: { session }, error: sessionError } = await supabaseClient.auth.getSession();
                if (sessionError) {
                    console.warn('Could not refresh session:', sessionError);
                }

                // 6. Notify parent component about the user update
                if (onUpdateUser) {
                    const updatedUser = session?.user || {
                        ...user,
                        user_metadata: {
                            ...user.user_metadata,
                            avatar_url: avatarDataUrl,
                            avatar_source: 'local'
                        }
                    };
                    onUpdateUser(updatedUser);
                }

                if (isBucketError) {
                    console.log('📦 Avatar stored locally (Supabase Storage bucket needs setup)');
                } else {
                    console.log('💾 Avatar stored locally as fallback');
                }
            }

            // Close the upload modal
            setUploadModal({ isOpen: false });

            // Show immediate local success toast (instant feedback within dashboard)
            showLocalToast('Profile photo updated successfully!', 'success');

        } catch (error) {
            console.error('❌ Avatar upload failed:', error);

            // Show error message with helpful details
            const errorMessage = error.message.includes('not authenticated')
                ? 'Please sign in again to upload your profile photo.'
                : error.message.includes('network') || error.message.includes('fetch')
                    ? 'Network error. Please check your connection and try again.'
                    : error.message.includes('Failed to store avatar data')
                        ? 'Storage full. Please free up space and try again.'
                        : `Upload failed: ${error.message}`;

            // Show immediate local error toast (instant feedback within dashboard)
            showLocalToast(errorMessage, 'error');

            throw error;
        } finally {
            setIsUploadingAvatar(false);
        }
    };

    // Add a helper function to get consistent avatar URL
    const getConsistentAvatarUrl = (user) => {
        if (!user?.user_metadata) return null;

        const metadata = user.user_metadata;

        // If it's a Supabase-stored avatar, ensure we use the storage URL
        if (metadata.avatar_source === 'supabase' && metadata.avatar_path) {
            const { data } = supabase.storage
                .from('user-avatars')
                .getPublicUrl(metadata.avatar_path);
            return data?.publicUrl || metadata.avatar_url;
        }

        // Otherwise use the stored URL
        return metadata.avatar_url;
    };

    // Enhanced Avatar deletion handler
    // Handle avatar delete with confirmation modal
    const handleDeleteAvatarWithConfirmation = () => {
        setConfirmModal({
            isOpen: true,
            title: 'Delete Profile Photo',
            message: 'Are you sure you want to delete your profile photo? This action cannot be undone.',
            onConfirm: performDeleteAvatar,
            itemToDelete: null
        });
    };

    // Perform actual avatar deletion (after confirmation)
    const performDeleteAvatar = async () => {
        try {
            setIsUploadingAvatar(true);

            if (!user?.id) {
                throw new Error('User not authenticated');
            }

            // Get current avatar URL to determine deletion strategy
            const currentAvatarUrl = getCurrentAvatarUrl();
            let storageCleanupError = null;

            // 1. Try to remove file from Supabase Storage (if it's a Supabase URL)
            if (currentAvatarUrl && typeof currentAvatarUrl === 'string') {
                try {
                    // Check if it's a Supabase Storage URL
                    const isSupabaseStorageUrl = currentAvatarUrl.includes('supabase.co/storage/v1/object/public/user-avatars') ||
                        currentAvatarUrl.includes('/storage/v1/object/public/user-avatars');

                    if (isSupabaseStorageUrl) {
                        // Extract file path from URL
                        const urlParts = currentAvatarUrl.split('/user-avatars/');
                        if (urlParts.length > 1) {
                            const filePath = urlParts[1];
                            console.log('Attempting to delete file from storage:', filePath);

                            // Remove from Supabase Storage
                            const { error: removeError } = await supabaseClient.storage
                                .from('user-avatars')
                                .remove([filePath]);

                            if (removeError) {
                                console.warn('Storage file removal failed:', removeError);
                                storageCleanupError = removeError;
                            } else {
                                console.log('✅ Avatar file removed from Supabase Storage successfully');
                            }
                        }
                    }
                } catch (storageError) {
                    console.warn('Storage cleanup error (non-critical):', storageError);
                    storageCleanupError = storageError;
                }
            }

            // 2. Clean up local storage (for fallback avatars)
            try {
                const avatarKeys = Object.keys(localStorage).filter(key =>
                    key.startsWith(`avatar_${user.id}`)
                );
                avatarKeys.forEach(key => {
                    try {
                        localStorage.removeItem(key);
                        console.log('Removed local avatar:', key);
                    } catch (e) {
                        console.warn('Could not remove local avatar key:', key, e);
                    }
                });
            } catch (localStorageError) {
                console.warn('Local storage cleanup error (non-critical):', localStorageError);
            }

            // 3. Remove avatar URL from user metadata
            const { error: updateError } = await supabaseClient.auth.updateUser({
                data: {
                    avatar_url: null,
                    avatar_source: null,
                    avatar_path: null
                }
            });

            if (updateError) {
                console.error('User metadata update error:', updateError);
                throw new Error(`Failed to update user: ${updateError.message}`);
            }

            // 4. Update local state
            // 5. Refresh the session to get the updated user metadata
            const { data: { session }, error: sessionError } = await supabaseClient.auth.getSession();
            if (sessionError) {
                console.warn('Could not refresh session after deletion:', sessionError);
            }

            // 6. Update local state
            setOptimisticAvatarUrl(null);
            setAvatarFile(null);

            // 7. Update current user state and notify parent component
            const freshUser = session?.user || user;
            setCurrentUser(freshUser);
            if (onUpdateUser) {
                onUpdateUser(freshUser);
            }

            // 8. Dispatch global event for other components
            window.dispatchEvent(
                new CustomEvent('userAvatarUpdated', {
                    detail: {
                        newUser: freshUser,
                        avatarUrl: null
                    }
                })
            );

            // 6. Close confirmation modal
            closeConfirmModal();

            // 7. Show success message (with storage warning if applicable)
            const successMessage = storageCleanupError
                ? 'Profile photo removed from your account. Note: Some storage cleanup may be pending.'
                : 'Your profile photo has been successfully removed.';

            // Show immediate local success toast (instant feedback within dashboard)
            showLocalToast(successMessage, 'success');

            console.log('✅ Avatar deletion completed successfully');

        } catch (error) {
            console.error('❌ Avatar deletion failed:', error);

            // Still attempt to clear local state for better UX
            try {
                setOptimisticAvatarUrl(null);
                setAvatarFile(null);

                // Try to clear local storage fallbacks
                const avatarKeys = Object.keys(localStorage).filter(key =>
                    key.startsWith(`avatar_${user?.id}`)
                );
                avatarKeys.forEach(key => {
                    try {
                        localStorage.removeItem(key);
                    } catch (e) {
                        // Silent fail for cleanup
                    }
                });

                console.log('Local state cleared despite server error');
            } catch (cleanupError) {
                console.warn('Could not clear local state:', cleanupError);
            }

            // Close confirmation modal
            closeConfirmModal();

            // Show error message with helpful details
            const errorMessage = error.message.includes('not authenticated')
                ? 'Please sign in again to delete your profile photo.'
                : error.message.includes('network') || error.message.includes('fetch')
                    ? 'Network error. Please check your connection and try again.'
                    : `Failed to delete profile photo: ${error.message}`;

            // Show immediate local error toast (instant feedback within dashboard)
            showLocalToast(errorMessage, 'error');
        } finally {
            setIsUploadingAvatar(false);
        }
    };

    // Original handleDeleteAvatar (kept for backward compatibility if needed)
    const handleDeleteAvatar = async () => {
        await performDeleteAvatar();
    };

    // Function to get current avatar URL (prioritize optimistic state, fallback to user metadata)
    const getCurrentAvatarUrl = () => {
        return optimisticAvatarUrl || getConsistentAvatarUrl(currentUser) || null;
    };

    // Local toast display function for immediate feedback with enhanced timing
    const showLocalToast = (message, type = 'success') => {
        setLocalToast({
            isVisible: true,
            message,
            type
        });

        // Auto-hide after 3 seconds
        setTimeout(() => {
            setLocalToast(prev => ({ ...prev, isVisible: false }));
        }, 3000);
    };

    // Diagnostic helper for debugging storage issues (accessible via console)
    const debugAvatarStorage = () => {
        console.log('🔍 Avatar Storage Diagnostic Report:');
        console.log('=====================================');

        // User info
        console.log('👤 User Info:');
        console.log('- User ID:', user?.id || 'Not authenticated');
        console.log('- Email:', user?.email || 'N/A');

        // Avatar state
        console.log('\n🖼️ Avatar State:');
        console.log('- Optimistic Avatar URL:', optimisticAvatarUrl || 'null');
        console.log('- Avatar Preview:', avatarPreview || 'null');
        console.log('- User Metadata Avatar:', currentUser?.user_metadata?.avatar_url || 'null');
        console.log('- Storage Type:', currentUser?.user_metadata?.avatar_source || 'unknown');
        console.log('- Current Avatar URL:', getCurrentAvatarUrl() || 'null');

        // Local storage check
        console.log('\n💾 Local Storage:');
        try {
            const avatarKeys = Object.keys(localStorage).filter(key => key.startsWith('avatar_'));
            console.log('- Avatar keys found:', avatarKeys.length);
            avatarKeys.forEach(key => {
                const size = localStorage.getItem(key)?.length || 0;
                console.log(`  - ${key}: ${(size / 1024).toFixed(1)}KB`);
            });
        } catch (e) {
            console.log('- Local storage error:', e.message);
        }

        // Storage test functions
        console.log('\n🧪 Test Functions:');
        console.log('- Test upload: window.testAvatarUpload()');
        console.log('- Test delete: window.testAvatarDelete()');
        console.log('- Clean local storage: window.cleanAvatarStorage()');

        console.log('=====================================');
    };

    // Expose debug functions to global window (for console access)
    React.useEffect(() => {
        if (typeof window !== 'undefined') {
            window.debugAvatarStorage = debugAvatarStorage;

            // Test upload permission
            window.testAvatarUpload = async () => {
                try {
                    const testBlob = new Blob(['test avatar content'], { type: 'text/plain' });
                    const testPath = `avatars/test-${user?.id || 'anonymous'}-${Date.now()}.txt`;

                    const { data, error } = await supabaseClient.storage
                        .from('user-avatars')
                        .upload(testPath, testBlob);

                    if (error) {
                        console.error('❌ Upload test failed:', error);
                        return false;
                    }

                    console.log('✅ Upload test successful:', data);

                    // Clean up test file
                    await supabaseClient.storage.from('user-avatars').remove([testPath]);
                    console.log('🧹 Test file cleaned up');
                    return true;
                } catch (error) {
                    console.error('❌ Upload test error:', error);
                    return false;
                }
            };

            // Test delete permission
            window.testAvatarDelete = async () => {
                try {
                    // First create a test file
                    const testBlob = new Blob(['delete test'], { type: 'text/plain' });
                    const testPath = `avatars/delete-test-${user?.id || 'anonymous'}-${Date.now()}.txt`;

                    const { error: uploadError } = await supabaseClient.storage
                        .from('user-avatars')
                        .upload(testPath, testBlob);

                    if (uploadError) {
                        console.error('❌ Could not create test file:', uploadError);
                        return false;
                    }

                    // Now try to delete it
                    const { error: deleteError } = await supabaseClient.storage
                        .from('user-avatars')
                        .remove([testPath]);

                    if (deleteError) {
                        console.error('❌ Delete test failed:', deleteError);
                        return false;
                    }

                    console.log('✅ Delete test successful');
                    return true;
                } catch (error) {
                    console.error('❌ Delete test error:', error);
                    return false;
                }
            };

            // Clean local avatar storage
            window.cleanAvatarStorage = () => {
                try {
                    const avatarKeys = Object.keys(localStorage).filter(key => key.startsWith('avatar_'));
                    avatarKeys.forEach(key => localStorage.removeItem(key));
                    console.log(`🧹 Cleaned ${avatarKeys.length} avatar items from localStorage`);
                    return true;
                } catch (error) {
                    console.error('❌ Clean storage error:', error);
                    return false;
                }
            };
        }

        return () => {
            if (typeof window !== 'undefined') {
                delete window.debugAvatarStorage;
                delete window.testAvatarUpload;
                delete window.testAvatarDelete;
                delete window.cleanAvatarStorage;
            }
        };
    }, [user, optimisticAvatarUrl, avatarPreview]);

    // Overview Tab Content
    const renderOverviewTab = () => {
        const creditsPercentage = (editableUser.credits / editableUser.maxCredits) * 100;

        // Calculate real statistics from actual generation history
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth();
        const currentYear = currentDate.getFullYear();

        const generationsThisMonth = generationHistory.filter(item => {
            if (!item.date) return false;
            try {
                const itemDate = new Date(item.date);
                return itemDate.getMonth() === currentMonth && itemDate.getFullYear() === currentYear;
            } catch {
                return false;
            }
        }).length;

        const last30DaysCount = generationHistory.filter(item => {
            if (!item.date) return false;
            try {
                const itemDate = new Date(item.date);
                const thirtyDaysAgo = new Date(currentDate.getTime() - (30 * 24 * 60 * 60 * 1000));
                return itemDate >= thirtyDaysAgo;
            } catch {
                return false;
            }
        }).length;

        const recentActivity = generationHistory
            .sort((a, b) => new Date(b.date || 0) - new Date(a.date || 0))
            .slice(0, 4);

        return React.createElement('div', { className: 'dashboard-overview p-6' },
            // Welcome Section
            React.createElement('div', { className: 'welcome-section mb-8' },
                React.createElement('div', { className: 'flex items-center justify-between' },
                    React.createElement('div', {},
                        React.createElement('h1', { className: 'text-3xl font-bold text-white mb-2' },
                            `Welcome back, ${editableUser.fullName}!`
                        ),
                        React.createElement('div', {
                            className: 'user-info-display-container flex items-center gap-4 text-gray-400',
                            id: 'user-info-display-container'
                        },
                            React.createElement('p', {
                                className: 'username-display-block',
                                id: 'username-display-block'
                            },
                                `@${editableUser.username || 'user'}`
                            ),
                            React.createElement('span', {
                                className: 'info-separator text-gray-600',
                                id: 'username-lastlogin-separator'
                            }, '•'),
                            React.createElement('p', {
                                className: 'last-login-display-block',
                                id: 'last-login-display-block'
                            },
                                `Last login: ${editableUser.lastLogin}`
                            )
                        )
                    )
                )
            ),



            // Promotional Banner Section
            React.createElement('div', { className: 'promotional-banner-section mb-8' },
                editableUser.plan === 'free' && React.createElement('div', {
                    className: 'upgrade-banner bg-gradient-to-r from-orange-600/20 via-orange-500/20 to-amber-500/20 border border-orange-500/30 rounded-xl p-6 relative overflow-hidden'
                },
                    // Background Pattern
                    React.createElement('div', {
                        className: 'banner-pattern absolute inset-0 opacity-10',
                        style: {
                            backgroundImage: 'radial-gradient(circle at 20% 80%, rgba(251, 146, 60, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%)',
                            backgroundSize: '100px 100px'
                        }
                    }),

                    // Banner Content
                    React.createElement('div', { className: 'banner-content relative z-10 flex items-center justify-between' },
                        // Left Side - Text Only (Icon Removed)
                        React.createElement('div', { className: 'flex items-center gap-4' },
                            // Text Content
                            React.createElement('div', { className: 'banner-text' },
                                React.createElement('h3', {
                                    className: 'text-xl font-bold text-orange-300 mb-1'
                                }, "You've reached your monthly limit!"),
                                React.createElement('p', {
                                    className: 'text-gray-300 text-sm'
                                }, "Upgrade to Pro to create unlimited thumbnails and access premium features.")
                            )
                        ),

                        // Right Side - CTA Button
                        React.createElement('div', { className: 'banner-action flex-shrink-0' },
                            React.createElement('button', {
                                className: 'auth-cta-btn dashboard-upgrade-cta-btn',
                                onClick: () => handleTabChange('billing'),
                                id: 'dashboard-upgrade-cta-btn'
                            },
                                React.createElement(Icon, {
                                    icon: 'solar:crown-bold',
                                    style: { fontSize: '18px' }
                                }),
                                React.createElement('span', { className: 'auth-cta-text' }, 'Upgrade to Pro')
                            )
                        )
                    )
                )
            ),

            // Stats Cards
            React.createElement('div', { className: 'stats-grid grid grid-cols-1 md:grid-cols-3 gap-6 mb-6' },
                // Monthly Credits Card
                React.createElement('div', { className: 'stat-card bg-gray-800 border border-gray-700 rounded-lg p-6' },
                    React.createElement('div', { className: 'flex items-center justify-between mb-4' },
                        React.createElement('h3', { className: 'text-lg font-semibold text-white' }, 'Monthly Credits'),
                        React.createElement(Icon, {
                            icon: 'solar:star-bold-duotone',
                            className: 'text-purple-400',
                            style: { fontSize: '24px' }
                        })
                    ),
                    React.createElement('div', { className: 'mb-3' },
                        React.createElement('span', { className: 'text-3xl font-bold text-white' }, editableUser.credits),
                        React.createElement('span', { className: 'text-gray-400 text-lg' }, ` / ${editableUser.maxCredits}`)
                    ),
                    React.createElement('div', { className: 'w-full bg-gray-700 rounded-full h-2 mb-2' },
                        React.createElement('div', {
                            className: 'bg-purple-500 h-2 rounded-full transition-all duration-300',
                            style: { width: `${creditsPercentage}%` }
                        })
                    ),
                    React.createElement('p', { className: 'text-sm text-gray-400' }, 'Resets monthly')
                ),

                // Generations Card
                React.createElement('div', { className: 'stat-card bg-gray-800 border border-gray-700 rounded-lg p-6' },
                    React.createElement('div', { className: 'flex items-center justify-between mb-4' },
                        React.createElement('h3', { className: 'text-lg font-semibold text-white' }, 'Generations'),
                        React.createElement(Icon, {
                            icon: 'solar:image-bold-duotone',
                            className: 'text-blue-400',
                            style: { fontSize: '24px' }
                        })
                    ),
                    React.createElement('div', { className: 'mb-3' },
                        React.createElement('span', { className: 'text-3xl font-bold text-white' }, generationsThisMonth)
                    ),
                    React.createElement('p', { className: 'text-sm text-gray-400' }, `${last30DaysCount} in the last 30 days`)
                ),

                // Subscription Card
                React.createElement('div', { className: 'stat-card bg-gray-800 border border-gray-700 rounded-lg p-6' },
                    React.createElement('div', { className: 'flex items-center justify-between mb-4' },
                        React.createElement('h3', { className: 'text-lg font-semibold text-white' }, 'Subscription'),
                        React.createElement(Icon, {
                            icon: 'solar:crown-bold-duotone',
                            className: 'text-green-400',
                            style: { fontSize: '24px' }
                        })
                    ),
                    React.createElement('div', { className: 'mb-3' },
                        React.createElement('span', { className: 'text-2xl font-bold text-white capitalize' }, editableUser.plan)
                    ),
                    React.createElement('p', { className: 'text-sm text-gray-400' }, `Renews ${editableUser.nextBilling}`)
                )
            )
        );
    };

    // Account Tab Content
    const renderAccountTab = () => {
        return React.createElement('div', { className: 'dashboard-account p-6' },
            // Personal Information Section
            React.createElement('div', {
                className: 'personal-info-section bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6',
                id: 'personal-info-section'
            },
                React.createElement('div', {
                    className: 'personal-info-header-container flex items-center justify-between mb-6',
                    id: 'personal-info-header-container'
                },
                    React.createElement('h2', {
                        className: 'personal-info-title text-xl font-bold text-white',
                        id: 'personal-info-title'
                    }, 'Personal Information'),
                    React.createElement('button', {
                        className: 'personal-info-edit-toggle-btn text-purple-400 hover:text-purple-300 text-sm font-medium flex items-center gap-2 transition-all duration-200',
                        id: 'personal-info-edit-toggle-btn',
                        onClick: () => setIsEditing(!isEditing)
                    },
                        React.createElement(Icon, {
                            icon: 'solar:pen-bold',
                            style: { fontSize: '16px' }
                        }),
                        isEditing ? 'Cancel' : 'Edit'
                    )
                ),
                React.createElement('div', {
                    className: `personal-info-main-content-container flex items-center gap-6 transition-all duration-350 ease-in-out ${isEditing ? 'personal-info-edit-mode' : ''}`,
                    id: 'personal-info-main-content-container',
                    style: {
                        flexDirection: window.innerWidth <= 768 && window.matchMedia('(orientation: portrait)').matches ? 'column' : 'row',
                        alignItems: window.innerWidth <= 768 && window.matchMedia('(orientation: portrait)').matches ? 'center' : 'flex-start',
                        textAlign: window.innerWidth <= 768 && window.matchMedia('(orientation: portrait)').matches ? 'center' : 'left'
                    }
                },
                    // Avatar Container with Upload Functionality (Edit Mode Only)
                    React.createElement('div', {
                        className: 'personal-info-avatar-container relative',
                        id: 'personal-info-avatar-container',
                        style: {
                            marginBottom: window.innerWidth <= 768 && window.matchMedia('(orientation: portrait)').matches ? '1.5rem' : '0'
                        }
                    },
                        React.createElement('div', {
                            className: 'personal-info-avatar-image-container w-20 h-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center relative overflow-hidden transition-all duration-200',
                            id: 'personal-info-avatar-image-container',
                            style: { alignItems: 'center' }
                        },
                            getCurrentAvatarUrl() ?
                                React.createElement('img', {
                                    src: getCurrentAvatarUrl(),
                                    alt: 'User Avatar',
                                    className: 'w-full h-full object-cover'
                                }) :
                                React.createElement(Icon, {
                                    icon: 'solar:user-bold',
                                    className: 'text-white',
                                    style: { fontSize: '32px', color: 'white' }
                                })
                        ),
                        // Camera Icon Overlay (only visible in edit mode)
                        isEditing && React.createElement('div', {
                            className: 'personal-info-avatar-camera-overlay absolute bottom-0 right-0 w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center border-2 border-gray-600 opacity-100 transition-all duration-200 cursor-pointer hover:bg-gray-700',
                            id: 'personal-info-avatar-camera-overlay',
                            onClick: () => setUploadModal({ isOpen: true }),
                            style: { transform: 'translate(25%, 25%)' },
                            title: 'Upload new photo'
                        },
                            React.createElement(Icon, {
                                icon: 'solar:camera-linear',
                                className: 'text-white',
                                style: { fontSize: '16px' }
                            })
                        ),
                        // Delete Avatar Icon (only visible in edit mode if avatar exists)
                        isEditing && getCurrentAvatarUrl() && React.createElement('div', {
                            className: 'personal-info-avatar-delete-overlay absolute top-0 right-0 w-6 h-6 bg-red-600 rounded-full flex items-center justify-center opacity-100 transition-all duration-200 cursor-pointer hover:bg-red-500',
                            id: 'personal-info-avatar-delete-overlay',
                            onClick: handleDeleteAvatarWithConfirmation,
                            style: { transform: 'translate(25%, -25%)' },
                            title: 'Delete photo'
                        },
                            React.createElement(Icon, {
                                icon: 'solar:trash-bin-minimalistic-linear',
                                className: 'text-white',
                                style: { fontSize: '12px' }
                            })
                        )
                    ),
                    // User Info Container - Wrapped with unique class
                    React.createElement('div', {
                        className: 'personal-info-form-fields-wrapper flex-1',
                        id: 'personal-info-form-fields-wrapper',
                        style: {
                            width: window.innerWidth <= 768 && window.matchMedia('(orientation: portrait)').matches ? '100%' : 'auto'
                        }
                    },
                        React.createElement('div', {
                            className: 'personal-info-fullname-field-container mb-4',
                            id: 'personal-info-fullname-field-container'
                        },
                            React.createElement('label', {
                                className: 'personal-info-fullname-label block text-sm font-medium text-gray-300 mb-2',
                                id: 'personal-info-fullname-label'
                            }, 'Full Name'),
                            isEditing ? React.createElement('input', {
                                type: 'text',
                                value: editableUser.fullName,
                                onChange: (e) => setEditableUser({ ...editableUser, fullName: e.target.value }),
                                className: 'personal-info-fullname-input w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white edit-transition focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-all duration-250',
                                id: 'personal-info-fullname-input'
                            }) : React.createElement('p', {
                                className: 'personal-info-fullname-display text-white font-medium edit-transition transition-all duration-250',
                                id: 'personal-info-fullname-display'
                            }, editableUser.fullName)
                        ),
                        React.createElement('div', {
                            className: 'personal-info-username-field-container mb-4',
                            id: 'personal-info-username-field-container'
                        },
                            React.createElement('label', {
                                className: 'personal-info-username-label block text-sm font-medium text-gray-300 mb-2',
                                id: 'personal-info-username-label'
                            }, 'Username'),
                            isEditing ? React.createElement('div', {
                                className: 'personal-info-username-input-wrapper relative',
                                id: 'personal-info-username-input-wrapper'
                            },
                                React.createElement('input', {
                                    type: 'text',
                                    value: editableUser.username,
                                    onChange: (e) => {
                                        // Only allow alphanumeric and underscores, convert to lowercase
                                        const value = e.target.value.toLowerCase().replace(/[^a-z0-9_]/g, '');
                                        setEditableUser({ ...editableUser, username: value });
                                    },
                                    placeholder: 'Enter your username',
                                    maxLength: 20,
                                    className: 'personal-info-username-input w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white edit-transition focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-all duration-250',
                                    id: 'personal-info-username-input'
                                }),
                                React.createElement('p', {
                                    className: 'personal-info-username-hint text-xs text-gray-500 mt-1',
                                    id: 'personal-info-username-hint'
                                }, 'Only lowercase letters, numbers, and underscores allowed. Max 20 characters.')
                            ) : React.createElement('div', {
                                className: 'personal-info-username-display-wrapper',
                                id: 'personal-info-username-display-wrapper'
                            },
                                React.createElement('p', {
                                    className: 'personal-info-username-display text-white font-medium edit-transition transition-all duration-250',
                                    id: 'personal-info-username-display'
                                }, `@${editableUser.username}`),
                                React.createElement('p', {
                                    className: 'personal-info-username-description text-xs text-gray-500 mt-1',
                                    id: 'personal-info-username-description'
                                }, 'Your unique username identifier')
                            )
                        ),
                        React.createElement('div', {
                            className: 'personal-info-email-field-container mb-4',
                            id: 'personal-info-email-field-container'
                        },
                            React.createElement('label', {
                                className: 'personal-info-email-label block text-sm font-medium text-gray-300 mb-2',
                                id: 'personal-info-email-label'
                            }, 'Email Address'),
                            isEditing ? React.createElement('input', {
                                type: 'email',
                                value: editableUser.email,
                                onChange: (e) => setEditableUser({ ...editableUser, email: e.target.value }),
                                className: 'personal-info-email-input w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white edit-transition focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-all duration-250',
                                id: 'personal-info-email-input'
                            }) : React.createElement('p', {
                                className: 'personal-info-email-display text-gray-400 edit-transition transition-all duration-250',
                                id: 'personal-info-email-display'
                            }, editableUser.email)
                        ),
                        React.createElement('div', {
                            className: 'personal-info-member-since-field-container mb-4',
                            id: 'personal-info-member-since-field-container'
                        },
                            React.createElement('label', {
                                className: 'personal-info-member-since-label block text-sm font-medium text-gray-300 mb-2',
                                id: 'personal-info-member-since-label'
                            }, 'Member Since'),
                            React.createElement('p', {
                                className: 'personal-info-member-since-display text-gray-400',
                                id: 'personal-info-member-since-display'
                            }, editableUser.memberSince)
                        )
                    )
                ),
                isEditing && React.createElement('div', {
                    className: 'personal-info-edit-controls-wrapper mt-6 transition-all duration-350 ease-in-out',
                    id: 'personal-info-edit-controls-wrapper',
                    style: {
                        opacity: isEditing ? 1 : 0,
                        transform: isEditing ? 'translateY(0)' : 'translateY(-10px)',
                        pointerEvents: isEditing ? 'auto' : 'none'
                    }
                },
                    React.createElement('div', {
                        className: 'personal-info-action-buttons-container flex gap-3',
                        id: 'personal-info-action-buttons-container'
                    },
                        React.createElement('button', {
                            onClick: handleSaveProfile,
                            disabled: isUploadingAvatar,
                            className: 'personal-info-save-action-btn bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed',
                            id: 'personal-info-save-action-btn'
                        },
                            isUploadingAvatar ? 'Saving...' : 'Save Changes'
                        ),
                        React.createElement('button', {
                            onClick: handleCancelEdit,
                            disabled: isUploadingAvatar,
                            className: 'personal-info-cancel-action-btn bg-gray-700 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50',
                            id: 'personal-info-cancel-action-btn'
                        }, 'Cancel')
                    )
                )
            ),

            // Security Section
            React.createElement('div', { className: 'security-section bg-gray-800 border border-gray-700 rounded-lg p-6' },
                React.createElement('h2', { className: 'text-xl font-bold text-white mb-6' }, 'Security'),

                // Password Change Section with Rate Limiting Info
                React.createElement('div', { className: 'security-item py-4 border-b border-gray-700' },
                    React.createElement('div', { className: 'flex items-start justify-between gap-4' },
                        React.createElement('div', { className: 'flex-1' },
                            React.createElement('h3', { className: 'text-white font-medium mb-2' }, 'Password'),

                            // Rate limiting status display
                            passwordRateLimit.isChecking ? (
                                React.createElement('div', { className: 'flex items-center gap-2 text-gray-400 text-sm mb-2' },
                                    React.createElement(Icon, {
                                        icon: 'solar:refresh-bold',
                                        className: 'animate-spin',
                                        style: { fontSize: '14px' }
                                    }),
                                    'Checking availability...'
                                )
                            ) : passwordRateLimit.message ? (
                                React.createElement('div', {
                                    className: `flex items-start gap-2 text-sm mb-2 ${passwordRateLimit.message.type === 'success' ? 'text-green-400' :
                                            passwordRateLimit.message.type === 'warning' ? 'text-yellow-400' :
                                                passwordRateLimit.message.type === 'info' ? 'text-blue-400' :
                                                    'text-red-400'
                                        }`
                                },
                                    React.createElement(Icon, {
                                        icon: passwordRateLimit.message.type === 'success' ? 'solar:check-circle-linear' :
                                            passwordRateLimit.message.type === 'warning' ? 'solar:clock-circle-linear' :
                                                passwordRateLimit.message.type === 'info' ? 'solar:info-circle-linear' :
                                                    'solar:danger-triangle-linear',
                                        className: 'mt-0.5',
                                        style: { fontSize: '16px' }
                                    }),
                                    React.createElement('div', {},
                                        React.createElement('p', { className: 'font-medium' }, passwordRateLimit.message.title),
                                        React.createElement('p', { className: 'text-xs opacity-90 mt-1' }, passwordRateLimit.message.message)
                                    )
                                )
                            ) : null,

                            // Last password change info
                            passwordRateLimit.eligibility?.lastChange ? (
                                React.createElement('p', { className: 'text-gray-400 text-sm' },
                                    `Last changed ${new Date(passwordRateLimit.eligibility.lastChange).toLocaleDateString()}`
                                )
                            ) : (
                                React.createElement('p', { className: 'text-gray-400 text-sm' }, 'Password security settings')
                            )
                        ),

                        // Password change button with conditional styling
                        React.createElement('button', {
                            className: `px-4 py-2 rounded-lg font-medium change-password-btn transition-all duration-200 ${passwordRateLimit.canChange && !passwordRateLimit.isChecking
                                    ? 'bg-gray-700 hover:bg-gray-600 text-white hover:shadow-lg'
                                    : 'bg-gray-800 text-gray-500 cursor-not-allowed border border-gray-600'
                                }`,
                            'data-testid': 'change-password-btn',
                            'data-cy': 'change-password-btn',
                            id: 'change-password-btn',
                            disabled: !passwordRateLimit.canChange || passwordRateLimit.isChecking,
                            onClick: () => {
                                if (passwordRateLimit.canChange && !passwordRateLimit.isChecking) {
                                    setPasswordModal(prev => ({ ...prev, isOpen: true }));
                                }
                            },
                            title: passwordRateLimit.canChange
                                ? 'Change your password'
                                : passwordRateLimit.message?.message || 'Password change not available'
                        },
                            passwordRateLimit.isChecking ? 'Checking...' : 'Change Password'
                        )
                    )
                ),

                // Remember Me Settings Section
                React.createElement('div', { className: 'security-item py-4 border-b border-gray-700' },
                    React.createElement('div', { className: 'flex items-start justify-between gap-4' },
                        React.createElement('div', { className: 'flex-1' },
                            React.createElement('h3', { className: 'text-white font-medium mb-2' }, 'Login Preferences'),
                            React.createElement('p', { className: 'text-gray-400 text-sm mb-2' },
                                rememberMeAuth.isRememberMeEnabled()
                                    ? 'Your login is saved for automatic sign-in on this device.'
                                    : 'You need to sign in each time you visit.'
                            ),
                            rememberMeAuth.isRememberMeEnabled() && React.createElement('p', { className: 'text-blue-400 text-xs' },
                                'This device will automatically sign you in until you sign out or clear your data.'
                            )
                        ),
                        rememberMeAuth.isRememberMeEnabled() && React.createElement('button', {
                            className: 'bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors',
                            onClick: () => {
                                setConfirmModal({
                                    isOpen: true,
                                    title: 'Forget This Device',
                                    message: 'This will remove your saved login information from this device. You will need to sign in manually next time. Continue?',
                                    onConfirm: () => {
                                        rememberMeAuth.clearSavedSession();
                                        closeConfirmModal();
                                        // Show success message
                                        console.log('✅ Remember Me data cleared');
                                    },
                                    itemToDelete: null
                                });
                            }
                        }, 'Forget This Device')
                    )
                ),

                React.createElement('div', { className: 'security-item mt-6 bg-red-900/20 border border-red-700/50 rounded-lg p-4' },
                    React.createElement('div', { className: 'flex items-start gap-3' },
                        React.createElement(Icon, {
                            icon: 'solar:danger-triangle-linear',
                            className: 'text-red-400 mt-1',
                            style: { fontSize: '20px' }
                        }),
                        React.createElement('div', { className: 'flex-1' },
                            React.createElement('h3', { className: 'text-red-400 font-medium mb-2' }, 'Delete Account'),
                            React.createElement('p', { className: 'text-gray-400 text-sm mb-4' }, 'Permanently delete your account and all associated data. This action cannot be undone.'),
                            React.createElement('button', {
                                className: 'border border-red-600 hover:border-red-500 text-red-400 hover:text-red-300 hover:bg-red-600/10 px-4 py-2 rounded-lg font-medium transition-colors',
                                onClick: handleDeleteAccount
                            }, 'Delete Account')
                        )
                    )
                )
            )
        );
    };

    // Billing Tab Content
    const renderBillingTab = () => {
        const creditsPercentage = (editableUser.credits / editableUser.maxCredits) * 100;

        return React.createElement('div', { className: 'dashboard-billing p-6' },
            // Subscription Section - Enhanced with better alignment
            React.createElement('div', { className: 'subscription-section bg-gray-800 border border-gray-700 rounded-xl p-8 mb-8 shadow-lg' },
                // Header Section with improved alignment
                React.createElement('div', { className: 'subscription-header flex items-center justify-between mb-8' },
                    React.createElement('div', { className: 'flex items-center gap-4' },
                        React.createElement('div', { className: 'flex items-center gap-3' },
                            React.createElement('h2', { className: 'text-2xl font-bold text-white' }, 'Subscription'),
                            React.createElement('span', { className: 'bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold shadow-sm' }, 'Active')
                        )
                    ),
                    React.createElement('div', { className: 'subscription-actions flex items-center gap-4' },
                        React.createElement('button', {
                            className: 'subscription-action-button upgrade-button px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-purple-500/25',
                            onClick: () => handleOpenPricingModal('pro'),
                            'aria-label': 'Upgrade to Pro Plan'
                        }, 'Upgrade Plan'),
                        React.createElement('button', {
                            className: 'cancel-renewal-btn text-gray-400 hover:text-gray-300 px-4 py-2.5 rounded-lg text-sm font-medium border border-gray-600 hover:border-gray-500 transition-all duration-200'
                        }, 'Cancel Renewal')
                    )
                ),

                // Subscription Details Grid - Enhanced alignment
                React.createElement('div', { className: 'subscription-details-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8' },
                    React.createElement('div', { className: 'subscription-detail-item' },
                        React.createElement('div', { className: 'detail-header mb-3' },
                            React.createElement('p', { className: 'text-gray-400 text-sm font-medium uppercase tracking-wide' }, 'Current Plan'),
                            React.createElement('div', { className: 'w-8 h-0.5 bg-purple-500 mt-1 rounded-full' })
                        ),
                        React.createElement('p', { className: 'text-white text-xl font-bold capitalize' }, editableUser.plan)
                    ),
                    React.createElement('div', { className: 'subscription-detail-item' },
                        React.createElement('div', { className: 'detail-header mb-3' },
                            React.createElement('p', { className: 'text-gray-400 text-sm font-medium uppercase tracking-wide' }, 'Price'),
                            React.createElement('div', { className: 'w-8 h-0.5 bg-green-500 mt-1 rounded-full' })
                        ),
                        React.createElement('p', { className: 'text-white text-xl font-bold' }, '12 CAD/month')
                    ),
                    React.createElement('div', { className: 'subscription-detail-item' },
                        React.createElement('div', { className: 'detail-header mb-3' },
                            React.createElement('p', { className: 'text-gray-400 text-sm font-medium uppercase tracking-wide' }, 'Billing Cycle'),
                            React.createElement('div', { className: 'w-8 h-0.5 bg-blue-500 mt-1 rounded-full' })
                        ),
                        React.createElement('p', { className: 'text-white text-xl font-bold mb-2' }, 'Monthly'),
                        React.createElement('button', {
                            className: 'change-billing-btn text-purple-400 hover:text-purple-300 text-sm font-medium transition-colors duration-200'
                        }, 'Change to Annual')
                    ),
                    React.createElement('div', { className: 'subscription-detail-item' },
                        React.createElement('div', { className: 'detail-header mb-3' },
                            React.createElement('p', { className: 'text-gray-400 text-sm font-medium uppercase tracking-wide' }, 'Next Payment'),
                            React.createElement('div', { className: 'w-8 h-0.5 bg-yellow-500 mt-1 rounded-full' })
                        ),
                        React.createElement('p', { className: 'text-white text-xl font-bold' }, editableUser.nextBilling)
                    )
                )
            ),

            // Credits Section - Enhanced with better visual hierarchy
            React.createElement('div', { className: 'credits-section bg-gray-800 border border-gray-700 rounded-xl p-8 mb-8 shadow-lg' },
                React.createElement('div', { className: 'credits-header flex items-center justify-between mb-8' },
                    React.createElement('div', { className: 'flex items-center gap-3' },
                        React.createElement('h2', { className: 'text-2xl font-bold text-white' }, 'Credits'),
                        React.createElement(Icon, {
                            icon: 'solar:lightning-bold',
                            className: 'text-yellow-400',
                            style: { fontSize: '28px' }
                        })
                    ),
                    React.createElement('div', { className: 'credits-summary text-right' },
                        React.createElement('p', { className: 'text-gray-400 text-sm mb-1' }, 'Credits Available'),
                        React.createElement('p', { className: 'text-white text-2xl font-bold' }, `${editableUser.credits}/${editableUser.maxCredits}`)
                    )
                ),

                // Credits Progress Bar - Enhanced design
                React.createElement('div', { className: 'credits-progress-container mb-8' },
                    React.createElement('div', { className: 'w-full bg-gray-700 rounded-full h-4 mb-4 shadow-inner' },
                        React.createElement('div', {
                            className: 'bg-gradient-to-r from-purple-500 via-blue-500 to-green-500 h-4 rounded-full transition-all duration-500 shadow-sm',
                            style: { width: `${creditsPercentage}%` }
                        })
                    ),
                    React.createElement('div', { className: 'flex items-center justify-between text-sm' },
                        React.createElement('span', { className: 'text-gray-400' }, `${Math.round(creditsPercentage)}% used this month`),
                        React.createElement('span', { className: 'text-gray-400 flex items-center gap-1' },
                            React.createElement(Icon, {
                                icon: 'solar:refresh-bold',
                                className: 'text-green-400',
                                style: { fontSize: '16px' }
                            }),
                            'Resets on 1st of each month'
                        )
                    )
                ),

                // Credit Usage Breakdown - Enhanced layout
                React.createElement('div', { className: 'credit-usage-breakdown' },
                    React.createElement('div', { className: 'flex items-center gap-3 mb-6' },
                        React.createElement(Icon, {
                            icon: 'solar:pie-chart-2-bold-duotone',
                            className: 'text-purple-400',
                            style: { fontSize: '24px' }
                        }),
                        React.createElement('h3', { className: 'text-xl font-bold text-white' }, 'Credit Usage Breakdown')
                    ),
                    React.createElement('div', { className: 'usage-grid grid grid-cols-1 md:grid-cols-3 gap-6' },
                        React.createElement('div', { className: 'usage-item bg-gray-750 rounded-lg p-6 border border-gray-600' },
                            React.createElement('div', { className: 'flex items-center justify-between mb-3' },
                                React.createElement('div', { className: 'flex items-center gap-2' },
                                    React.createElement('div', { className: 'w-3 h-3 bg-green-400 rounded-full' }),
                                    React.createElement('span', { className: 'text-gray-300 font-medium' }, 'Low Quality')
                                ),
                                React.createElement('span', { className: 'text-green-400 font-bold text-lg' }, '1 credit')
                            ),
                            React.createElement('p', { className: 'text-gray-400 text-sm' }, 'Fast generation, good for previews')
                        ),
                        React.createElement('div', { className: 'usage-item bg-gray-750 rounded-lg p-6 border border-gray-600' },
                            React.createElement('div', { className: 'flex items-center justify-between mb-3' },
                                React.createElement('div', { className: 'flex items-center gap-2' },
                                    React.createElement('div', { className: 'w-3 h-3 bg-blue-400 rounded-full' }),
                                    React.createElement('span', { className: 'text-gray-300 font-medium' }, 'Medium Quality')
                                ),
                                React.createElement('span', { className: 'text-blue-400 font-bold text-lg' }, '3 credits')
                            ),
                            React.createElement('p', { className: 'text-gray-400 text-sm' }, 'Balanced quality and speed')
                        ),
                        React.createElement('div', { className: 'usage-item bg-gray-750 rounded-lg p-6 border border-gray-600' },
                            React.createElement('div', { className: 'flex items-center justify-between mb-3' },
                                React.createElement('div', { className: 'flex items-center gap-2' },
                                    React.createElement('div', { className: 'w-3 h-3 bg-purple-400 rounded-full' }),
                                    React.createElement('span', { className: 'text-gray-300 font-medium' }, 'HD Quality')
                                ),
                                React.createElement('span', { className: 'text-purple-400 font-bold text-lg' }, '5 credits')
                            ),
                            React.createElement('p', { className: 'text-gray-400 text-sm' }, 'Premium quality, best results')
                        )
                    )
                )
            ),

            // Billing Information Section - Enhanced
            React.createElement('div', { className: 'billing-info-section bg-gray-800 border border-gray-700 rounded-xl p-8 shadow-lg' },
                React.createElement('div', { className: 'billing-header flex items-center justify-between mb-8' },
                    React.createElement('div', { className: 'flex items-center gap-3' },
                        React.createElement('h2', { className: 'text-2xl font-bold text-white' }, 'Billing Information'),
                        React.createElement(Icon, {
                            icon: 'solar:card-bold-duotone',
                            className: 'text-green-400',
                            style: { fontSize: '28px' }
                        })
                    ),
                    billingEditMode ?
                        React.createElement('div', { className: 'flex gap-3' },
                            React.createElement('button', {
                                onClick: handleSaveBilling,
                                disabled: isSavingBilling,
                                className: 'bg-green-600 hover:bg-green-700 disabled:bg-green-700 disabled:opacity-50 text-white px-6 py-2.5 rounded-lg text-sm font-semibold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 disabled:transform-none'
                            }, isSavingBilling ? 'Saving...' : 'Save Changes'),
                            React.createElement('button', {
                                onClick: handleCancelBilling,
                                disabled: isSavingBilling,
                                className: 'bg-gray-600 hover:bg-gray-700 disabled:bg-gray-700 disabled:opacity-50 text-white px-6 py-2.5 rounded-lg text-sm font-semibold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 disabled:transform-none'
                            }, 'Cancel')
                        ) :
                        React.createElement('button', {
                            onClick: handleEditBilling,
                            className: 'edit-billing-btn bg-purple-600 hover:bg-purple-700 text-white px-6 py-2.5 rounded-lg text-sm font-semibold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105'
                        }, 'Edit Information')
                ),

                billingEditMode ?
                    // Edit Mode Form
                    React.createElement('form', {
                        className: 'billing-edit-form',
                        onSubmit: handleSaveBilling
                    },
                        // Two-column grid for form fields
                        React.createElement('div', { className: 'billing-form-grid grid grid-cols-1 md:grid-cols-2 gap-6 mb-6' },
                            // Full Name (Left Column)
                            React.createElement('div', { className: 'form-group' },
                                React.createElement('label', { className: 'block text-sm font-medium text-gray-300 mb-2' }, 'Full Name *'),
                                React.createElement('input', {
                                    type: 'text',
                                    value: billingFormData.fullName,
                                    onChange: (e) => handleBillingFieldChange('fullName', e.target.value),
                                    className: `w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all ${billingErrors.fullName ? 'border-red-500' : 'border-gray-600'
                                        }`,
                                    placeholder: 'Enter your full name'
                                }),
                                billingErrors.fullName && React.createElement('p', { className: 'mt-1 text-sm text-red-400' }, billingErrors.fullName)
                            ),

                            // Email (Right Column)
                            React.createElement('div', { className: 'form-group' },
                                React.createElement('label', { className: 'block text-sm font-medium text-gray-300 mb-2' }, 'Email Address *'),
                                React.createElement('input', {
                                    type: 'email',
                                    value: billingFormData.email,
                                    onChange: (e) => handleBillingFieldChange('email', e.target.value),
                                    className: `w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all ${billingErrors.email ? 'border-red-500' : 'border-gray-600'
                                        }`,
                                    placeholder: 'Enter your email address'
                                }),
                                billingErrors.email && React.createElement('p', { className: 'mt-1 text-sm text-red-400' }, billingErrors.email)
                            ),

                            // Phone Number (Left Column)
                            React.createElement('div', { className: 'form-group' },
                                React.createElement('label', { className: 'block text-sm font-medium text-gray-300 mb-2' }, 'Phone Number'),
                                React.createElement('input', {
                                    type: 'tel',
                                    value: billingFormData.phone,
                                    onChange: (e) => handleBillingFieldChange('phone', e.target.value),
                                    className: 'w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all',
                                    placeholder: '+****************'
                                })
                            ),

                            // Country (Right Column)
                            React.createElement('div', { className: 'form-group' },
                                React.createElement('label', { className: 'block text-sm font-medium text-gray-300 mb-2' }, 'Country *'),
                                React.createElement(SearchableCountrySelect, {
                                    value: billingFormData.country,
                                    onChange: (country) => handleBillingFieldChange('country', country),
                                    countries: countries,
                                    placeholder: 'Select your country',
                                    error: !!billingErrors.country,
                                    className: 'w-full'
                                }),
                                billingErrors.country && React.createElement('p', { className: 'mt-1 text-sm text-red-400' }, billingErrors.country)
                            ),

                            // Address (Left Column)
                            React.createElement('div', { className: 'form-group' },
                                React.createElement('label', { className: 'block text-sm font-medium text-gray-300 mb-2' }, 'Address *'),
                                React.createElement('input', {
                                    type: 'text',
                                    value: billingFormData.address,
                                    onChange: (e) => handleBillingFieldChange('address', e.target.value),
                                    className: `w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all ${billingErrors.address ? 'border-red-500' : 'border-gray-600'
                                        }`,
                                    placeholder: 'Enter your street address'
                                }),
                                billingErrors.address && React.createElement('p', { className: 'mt-1 text-sm text-red-400' }, billingErrors.address)
                            ),

                            // Postal Code (Right Column)
                            React.createElement('div', { className: 'form-group' },
                                React.createElement('label', { className: 'block text-sm font-medium text-gray-300 mb-2' }, 'Postal Code *'),
                                React.createElement('input', {
                                    type: 'text',
                                    value: billingFormData.postalCode,
                                    onChange: (e) => handleBillingFieldChange('postalCode', e.target.value),
                                    className: `w-full px-4 py-3 bg-gray-700 border rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all ${billingErrors.postalCode ? 'border-red-500' : 'border-gray-600'
                                        }`,
                                    placeholder: 'Enter postal code'
                                }),
                                billingErrors.postalCode && React.createElement('p', { className: 'mt-1 text-sm text-red-400' }, billingErrors.postalCode)
                            )
                        ),

                        // Action buttons
                        React.createElement('div', { className: 'flex gap-3 justify-end' },
                            React.createElement('button', {
                                type: 'button',
                                onClick: handleCancelBilling,
                                className: 'px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-all focus:ring-2 focus:ring-gray-400 focus:outline-none'
                            }, 'Cancel'),
                            React.createElement('button', {
                                type: 'submit',
                                disabled: isSavingBilling,
                                className: 'px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all focus:ring-2 focus:ring-purple-400 focus:outline-none flex items-center gap-2'
                            },
                                isSavingBilling && React.createElement('div', { className: 'w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' }),
                                isSavingBilling ? 'Saving...' : 'Save Changes'
                            )
                        )
                    ) :
                    // View Mode
                    React.createElement('div', { className: 'billing-details-grid grid grid-cols-1 md:grid-cols-2 gap-8' },
                        React.createElement('div', { className: 'billing-detail-item' },
                            React.createElement('div', { className: 'detail-header mb-3' },
                                React.createElement('p', { className: 'text-gray-400 text-sm font-medium uppercase tracking-wide' }, 'Billing Name'),
                                React.createElement('div', { className: 'w-8 h-0.5 bg-blue-500 mt-1 rounded-full' })
                            ),
                            React.createElement('p', { className: 'text-white text-lg font-semibold' }, editableUser.fullName || 'Not provided')
                        ),
                        React.createElement('div', { className: 'billing-detail-item' },
                            React.createElement('div', { className: 'detail-header mb-3' },
                                React.createElement('p', { className: 'text-gray-400 text-sm font-medium uppercase tracking-wide' }, 'Billing Email'),
                                React.createElement('div', { className: 'w-8 h-0.5 bg-green-500 mt-1 rounded-full' })
                            ),
                            React.createElement('p', { className: 'text-white text-lg font-semibold' }, editableUser.email || 'Not provided')
                        ),
                        React.createElement('div', { className: 'billing-detail-item' },
                            React.createElement('div', { className: 'detail-header mb-3' },
                                React.createElement('p', { className: 'text-gray-400 text-sm font-medium uppercase tracking-wide' }, 'Phone Number'),
                                React.createElement('div', { className: 'w-8 h-0.5 bg-purple-500 mt-1 rounded-full' })
                            ),
                            React.createElement('p', { className: 'text-white text-lg font-semibold' }, editableUser.phone || 'Not provided')
                        ),
                        React.createElement('div', { className: 'billing-detail-item' },
                            React.createElement('div', { className: 'detail-header mb-3' },
                                React.createElement('p', { className: 'text-gray-400 text-sm font-medium uppercase tracking-wide' }, 'Country'),
                                React.createElement('div', { className: 'w-8 h-0.5 bg-yellow-500 mt-1 rounded-full' })
                            ),
                            React.createElement('p', { className: 'text-white text-lg font-semibold' }, editableUser.country || 'Not provided')
                        ),
                        React.createElement('div', { className: 'billing-detail-item' },
                            React.createElement('div', { className: 'detail-header mb-3' },
                                React.createElement('p', { className: 'text-gray-400 text-sm font-medium uppercase tracking-wide' }, 'Address'),
                                React.createElement('div', { className: 'w-8 h-0.5 bg-red-500 mt-1 rounded-full' })
                            ),
                            React.createElement('p', { className: 'text-white text-lg font-semibold' }, editableUser.address || 'Not provided')
                        ),
                        React.createElement('div', { className: 'billing-detail-item' },
                            React.createElement('div', { className: 'detail-header mb-3' },
                                React.createElement('p', { className: 'text-gray-400 text-sm font-medium uppercase tracking-wide' }, 'Postal Code'),
                                React.createElement('div', { className: 'w-8 h-0.5 bg-indigo-500 mt-1 rounded-full' })
                            ),
                            React.createElement('p', { className: 'text-white text-lg font-semibold' }, editableUser.postalCode || 'Not provided')
                        )
                    )
            )
        );
    };

    // Generation History Tab Content
    const renderHistoryTab = () => {
        // Helper function to properly close a dropdown
        const closeDropdown = (element) => {
            if (!element) return; // Guard against null element
            const dropdown = element.closest('.dropdown-menu');
            if (dropdown) {
                dropdown.style.display = 'none';
            }
        };

        return React.createElement('div', { className: 'dashboard-history p-6' },
            React.createElement('div', { className: 'flex items-center justify-between mb-6' },
                React.createElement('h1', { className: 'text-2xl font-bold text-white' }, 'Generation History'),
                React.createElement('div', {
                    className: 'flex gap-3',
                    id: 'generation-history-filter-controls'
                },
                    React.createElement('button', {
                        className: 'bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2',
                        id: 'generation-history-sort-btn',
                        onClick: handleSortToggle
                    },
                        sortOrder === 'newest' ? 'Newest First' : 'Oldest First',
                        React.createElement(Icon, {
                            icon: sortOrder === 'newest' ? 'solar:sort-vertical-bold' : 'solar:sort-vertical-bold',
                            style: { fontSize: '16px' }
                        })
                    ),
                    React.createElement('button', {
                        className: 'bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2',
                        id: 'generation-history-quality-filter-btn',
                        onClick: handleQualityFilterToggle
                    },
                        getQualityFilterLabel(),
                        React.createElement(Icon, {
                            icon: 'solar:filter-bold',
                            style: { fontSize: '16px' }
                        })
                    )
                )
            ),
            (filteredAndSortedHistory.length > 0 && !debugEmptyState) ? React.createElement('div', { className: 'history-grid' },
                filteredAndSortedHistory.map(item =>
                    React.createElement('div', {
                        key: item.id || item.timestamp,
                        className: 'history-item',
                        onMouseLeave: (e) => {
                            // Close dropdown when mouse leaves the history item
                            const dropdown = e.currentTarget.querySelector('.dropdown-menu');
                            if (dropdown && dropdown.style.display === 'flex') {
                                dropdown.style.display = 'none';
                            }
                        },

                    },
                        React.createElement('div', { className: 'relative' },
                            item.thumbnail ?
                                // Display the actual thumbnail covering entire card
                                React.createElement('img', {
                                    src: item.thumbnail,
                                    alt: item.title || item.prompt || 'Generated thumbnail',
                                    className: 'history-item-thumbnail'
                                }) :
                                // Fallback placeholder for old entries without thumbnails
                                React.createElement('div', { className: 'history-item-placeholder' },
                                    React.createElement(Icon, {
                                        icon: 'solar:gallery-bold-duotone',
                                        className: 'text-4xl mb-2 text-purple-400'
                                    }),
                                    React.createElement('div', { className: 'text-sm text-center' },
                                        React.createElement('div', { className: 'font-medium text-white mb-1' }, (item.quality || 'Medium') + ' Quality'),
                                        React.createElement('div', { className: 'text-gray-400' }, (item.credits || 3) + ' credits used')
                                    )
                                ),

                            // Dark feather gradient overlay at bottom
                            React.createElement('div', { className: 'history-item-gradient' }),

                            // Bottom Actions Container - revealed on hover
                            React.createElement('div', { className: 'bottom-actions' },
                                // Left button - View Details
                                React.createElement('button', {
                                    className: 'view-details-btn',
                                    id: `view-details-${item.id || item.timestamp}`,
                                    onClick: () => {
                                        setDetailsModal({
                                            isOpen: true,
                                            details: item
                                        });
                                    }
                                },
                                    React.createElement(Icon, {
                                        icon: 'solar:clipboard-text-linear',
                                        style: { fontSize: '16px' }
                                    }),
                                    'View Details'
                                ),

                                // Right button container - Show More with dropdown
                                React.createElement('div', { className: 'show-more-container' },
                                    React.createElement('button', {
                                        className: 'show-more-btn',
                                        id: `show-more-${item.id || item.timestamp}`,
                                        onClick: (e) => {
                                            e.stopPropagation();
                                            const dropdown = e.currentTarget.nextElementSibling;
                                            const isVisible = dropdown.style.display === 'flex';

                                            // Close all other dropdowns
                                            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                                                if (menu !== dropdown) {
                                                    menu.style.display = 'none';
                                                }
                                            });

                                            // Toggle current dropdown
                                            dropdown.style.display = isVisible ? 'none' : 'flex';
                                        }
                                    },
                                        React.createElement(Icon, {
                                            icon: 'solar:menu-dots-bold',
                                            style: { fontSize: '16px' }
                                        })
                                    ),

                                    // Dropdown Menu
                                    React.createElement('div', {
                                        id: `dropdown-menu-${item.id || item.timestamp}`,
                                        className: 'dropdown-menu',
                                        onClick: (e) => e.stopPropagation(), // Prevent click from bubbling to document
                                        style: { display: 'none' }
                                    },
                                        // Download option
                                        React.createElement('button', {
                                            className: 'dropdown-item',
                                            onClick: async (e) => {
                                                e.stopPropagation();
                                                // Use full-quality image from Supabase if available, otherwise fallback to thumbnail
                                                const downloadUrl = item.fullImageUrl || item.thumbnail;
                                                await downloadThumbnailAt1280x720(downloadUrl, item.id, downloadUrl);
                                                closeDropdown(e.currentTarget);
                                            }
                                        },
                                            React.createElement(Icon, {
                                                icon: 'solar:download-minimalistic-linear',
                                                style: { fontSize: '16px' }
                                            }),
                                            'Download'
                                        ),

                                        // Preview option
                                        React.createElement('button', {
                                            className: 'dropdown-item',
                                            onClick: (e) => {
                                                e.stopPropagation();
                                                if (item.thumbnail) {
                                                    setPreviewModal({
                                                        isOpen: true,
                                                        thumbnail: item.thumbnail,
                                                        title: item.title || item.prompt || 'Generated Thumbnail',
                                                        itemId: item.id || item.timestamp,
                                                        fullImageUrl: item.fullImageUrl
                                                    });
                                                }
                                                closeDropdown(e.currentTarget);
                                            }
                                        },
                                            React.createElement(Icon, {
                                                icon: 'solar:eye-linear',
                                                style: { fontSize: '16px' }
                                            }),
                                            'Preview'
                                        ),

                                        // Delete option
                                        React.createElement('button', {
                                            className: 'dropdown-item delete-item',
                                            onClick: (e) => {
                                                e.stopPropagation();
                                                console.log('Delete button clicked for item:', item);
                                                // Close dropdown
                                                closeDropdown(e.currentTarget);
                                                // Call delete handler
                                                handleDeleteGeneration(item);
                                            }
                                        },
                                            React.createElement(Icon, {
                                                icon: 'solar:trash-bin-2-linear',
                                                style: { fontSize: '16px' }
                                            }),
                                            'Delete'
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            ) : React.createElement('div', {
                className: 'history-empty-state text-center py-16 text-gray-400',
                id: 'dashboard-history-empty-state'
            },
                // Debug indicator
                debugEmptyState && React.createElement('div', {
                    className: 'debug-indicator mb-4 inline-flex items-center gap-2 px-3 py-1 bg-yellow-900/30 border border-yellow-500 rounded-full text-yellow-400 text-sm font-medium'
                },
                    React.createElement(Icon, {
                        icon: 'solar:bug-bold-duotone',
                        style: { fontSize: '14px' }
                    }),
                    'Debug Mode Active'
                ),
                // SVG Illustration (shown when debug mode is active) or Icon (normal mode)
                debugEmptyState ? React.createElement('div', {
                    className: 'empty-state-illustration mb-6 flex justify-center',
                    id: 'history-empty-illustration'
                },
                    React.createElement(Icon, {
                        icon: 'solar:gallery-bold-duotone',
                        className: 'dashboard-empty-state-icon text-gray-400',
                        style: {
                            fontSize: '94px',
                            width: '94px',
                            height: '93px',
                            display: 'block',
                            margin: '0 auto 24px auto'
                        }
                    })
                ) : React.createElement(Icon, {
                    icon: 'solar:gallery-bold-duotone',
                    className: 'dashboard-empty-state-icon empty-state-icon mb-6 text-gray-400',
                    style: {
                        fontSize: '94px',
                        width: '94px',
                        height: '93px',
                        display: 'block',
                        margin: '0 auto 24px auto'
                    }
                }),
                React.createElement('h3', {
                    className: 'empty-state-title text-xl font-medium text-gray-300 mb-4',
                    id: 'history-empty-title'
                },
                    generationHistory.length === 0 ? 'No generations yet' : 'No Matching Generations'
                ),
                React.createElement('p', {
                    className: 'empty-state-description text-gray-400 mb-6',
                    id: 'history-empty-description'
                },
                    generationHistory.length === 0
                        ? 'Start creating amazing thumbnails to build your generation history!'
                        : 'Try adjusting your filters to see more results.'
                ),
                React.createElement('button', {
                    onClick: onExitDashboard,
                    className: 'dashboard-empty-cta text-white font-medium rounded-full flex items-center justify-center gap-2 hover:bg-blue-700 hover:shadow-lg hover:scale-105 transition-all duration-300 ease-in-out',
                    id: 'dashboard-history-create-thumbnail-btn',
                    style: {
                        display: 'flex',
                        height: '3rem',
                        padding: '0rem 1.5rem',
                        justifyContent: 'center',
                        alignItems: 'center',
                        background: '#006FEE',
                        margin: 'auto',
                        position: 'relative',
                        overflow: 'hidden'
                    }
                },
                    React.createElement(Icon, {
                        icon: 'solar:magic-stick-3-bold-duotone',
                        style: { fontSize: '20px' }
                    }),
                    'Create Your First Thumbnail'
                )
            )
        );
    };

    // ================= SUBSCRIPTION OVERLAY RENDER FUNCTION (REMOVED) =================
    // Note: This function has been replaced by renderPricingModal

    const renderSubscriptionOverlay = () => {
        return null; // Function disabled - using modal instead

        return React.createElement('div', {
            className: `subscription-overlay-backdrop fixed inset-0 z-[9998] bg-black/80 backdrop-blur-sm transition-all duration-300 ${subscriptionOverlay.isTransitioning ? 'opacity-0' : 'opacity-100'
                }`,
            onClick: handleCloseSubscriptionOverlay
        },
            React.createElement('div', {
                className: `subscription-overlay-container w-full h-full overflow-y-auto transition-all duration-300 ${subscriptionOverlay.isTransitioning ? 'translate-y-4 opacity-0' : 'translate-y-0 opacity-100'
                    }`,
                onClick: (e) => e.stopPropagation()
            },
                React.createElement('div', {
                    className: 'subscription-overlay-content min-h-screen bg-gray-900 px-6 py-8'
                },
                    // Header with back button
                    React.createElement('div', {
                        className: 'subscription-overlay-header max-w-7xl mx-auto mb-8'
                    },
                        React.createElement('div', {
                            className: 'flex items-center justify-between'
                        },
                            React.createElement('button', {
                                onClick: handleCloseSubscriptionOverlay,
                                className: 'back-button flex items-center gap-3 text-gray-400 hover:text-white transition-colors duration-200 group',
                                'aria-label': 'Go back to billing'
                            },
                                React.createElement('div', {
                                    className: 'back-icon-container bg-gray-800 hover:bg-gray-700 rounded-full p-2 transition-all duration-200 group-hover:scale-105'
                                },
                                    React.createElement(Icon, {
                                        icon: 'solar:arrow-left-bold',
                                        className: 'text-lg'
                                    })
                                ),
                                React.createElement('span', {
                                    className: 'text-sm font-medium'
                                }, 'Back to Billing')
                            ),
                            React.createElement('div', {
                                className: 'header-title text-center'
                            },
                                React.createElement('h1', {
                                    className: 'text-3xl font-bold text-white mb-2'
                                }, 'Choose Your Plan'),
                                React.createElement('p', {
                                    className: 'text-gray-400 text-lg'
                                }, 'Unlock the full potential of AI thumbnail generation')
                            ),
                            React.createElement('div', {
                                className: 'w-[120px]' // Spacer to balance the layout
                            })
                        )
                    ),

                    // Pricing plans grid
                    React.createElement('div', {
                        className: 'pricing-plans-grid max-w-7xl mx-auto'
                    },
                        React.createElement('div', {
                            className: 'grid grid-cols-1 lg:grid-cols-3 gap-8'
                        },
                            pricingPlans.map((plan, index) =>
                                React.createElement('div', {
                                    key: plan.id,
                                    className: `pricing-plan-card relative bg-gradient-to-b ${plan.isCurrentPlan
                                            ? 'from-gray-800/60 to-gray-900/80 border-gray-600/50'
                                            : plan.highlight
                                                ? 'from-purple-900/20 via-gray-800/60 to-purple-900/30 border-purple-500/30'
                                                : 'from-gray-800/40 to-gray-900/60 border-gray-700/40'
                                        } rounded-2xl p-8 border backdrop-blur-sm transition-all duration-300 hover:scale-[1.02] hover:shadow-xl ${plan.highlight ? 'hover:shadow-purple-500/20' : 'hover:shadow-gray-900/50'
                                        }`,
                                    style: plan.highlight ? {
                                        boxShadow: '0 20px 40px -12px rgba(147, 51, 234, 0.15)',
                                        transform: 'translateY(-4px)'
                                    } : {}
                                },
                                    // Popular badge
                                    plan.mostPopular && React.createElement('div', {
                                        className: 'popular-badge absolute -top-4 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg'
                                    }, 'Most Popular'),

                                    // Plan header
                                    React.createElement('div', {
                                        className: 'plan-header text-center mb-8'
                                    },
                                        React.createElement('h3', {
                                            className: 'text-2xl font-bold text-white mb-2'
                                        }, plan.name),
                                        React.createElement('p', {
                                            className: 'text-gray-400 text-sm mb-4'
                                        }, plan.description),
                                        React.createElement('div', {
                                            className: 'price-display flex items-baseline justify-center gap-1 mb-6'
                                        },
                                            React.createElement('span', {
                                                className: 'text-4xl font-bold text-white'
                                            }, plan.price),
                                            React.createElement('span', {
                                                className: 'text-gray-400 text-lg'
                                            }, plan.period)
                                        )
                                    ),

                                    // Features list
                                    React.createElement('div', {
                                        className: 'features-list mb-8'
                                    },
                                        React.createElement('ul', {
                                            className: 'space-y-3'
                                        },
                                            plan.features.map((feature, featureIndex) =>
                                                React.createElement('li', {
                                                    key: featureIndex,
                                                    className: 'flex items-center gap-3 text-gray-300'
                                                },
                                                    React.createElement(Icon, {
                                                        icon: 'solar:check-circle-bold',
                                                        className: 'text-green-400 flex-shrink-0'
                                                    }),
                                                    React.createElement('span', {
                                                        className: 'text-sm'
                                                    }, feature)
                                                )
                                            )
                                        )
                                    ),

                                    // Action button
                                    React.createElement('div', {
                                        className: 'plan-action'
                                    },
                                        React.createElement('button', {
                                            onClick: () => handlePlanSelect(plan.id),
                                            disabled: plan.isCurrentPlan,
                                            className: `w-full py-3 px-6 rounded-lg font-semibold transition-all duration-200 ${plan.isCurrentPlan
                                                    ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
                                                    : plan.highlight
                                                        ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl'
                                                        : 'bg-gray-700 hover:bg-gray-600 text-white hover:shadow-lg'
                                                }`
                                        }, plan.buttonText)
                                    )
                                )
                            )
                        )
                    )
                )
            )
        );
    };



    // Keyboard event handler for Esc key - updated for modal
    useEffect(() => {
        const handleKeyDown = (event) => {
            if (event.key === 'Escape') {
                if (isPricingModalOpen) {
                    handleClosePricingModal();
                }
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [isPricingModalOpen]);

    // ================= SUBSCRIPTION PRICING MODAL RENDER FUNCTION =================

    const renderPricingModal = () => {
        if (!isPricingModalOpen) return null;

        const getModalClasses = () => {
            let classes = 'fixed inset-0 z-[9999] overflow-y-auto overflow-x-hidden transition-all duration-300 ease-out';

            if (modalTransition.animationType === 'fadeIn') {
                classes += modalTransition.isAnimating ? ' modal-fade-in-start' : ' modal-fade-in-end';
            } else if (modalTransition.animationType === 'fadeOut') {
                classes += ' modal-fade-out';
            } else {
                classes += ' modal-fade-in-end';
            }

            return classes;
        };

        const getContentClasses = () => {
            let classes = 'relative modal-liquid-glass-container transition-all duration-300 ease-out';

            if (modalTransition.animationType === 'fadeIn') {
                classes += modalTransition.isAnimating ? ' modal-content-fade-in-start' : ' modal-content-fade-in-end';
            } else if (modalTransition.animationType === 'fadeOut') {
                classes += ' modal-content-fade-out';
            } else {
                classes += ' modal-content-fade-in-end';
            }

            return classes;
        };

        return React.createElement('div', {
            className: getModalClasses(),
            style: {
                backgroundColor: modalTransition.animationType === 'fadeOut' ? 'rgba(0, 0, 0, 0)' : 'rgba(0, 0, 0, 0.75)',
                backdropFilter: 'blur(20px) saturate(180%)',
                WebkitBackdropFilter: 'blur(20px) saturate(180%)',
                position: 'fixed',
                top: '0',
                left: '0',
                right: '0',
                bottom: '0',
                height: '100vh',
                width: '100vw',
                transition: 'all 300ms cubic-bezier(0.165, 0.84, 0.44, 1)',
                perspective: '1000px'
            },
            onClick: handleClosePricingModal
        },
            React.createElement('div', {
                className: 'w-full h-full flex items-center justify-center p-4 sm:p-6',
                style: { minHeight: '100vh' }
            },
                React.createElement('div', {
                    className: getContentClasses(),
                    onClick: (e) => e.stopPropagation()
                },
                    // Modal Header - Liquid Glass Style
                    React.createElement('div', {
                        className: 'modal-glass-header'
                    },
                        // Close Button - Top Right
                        React.createElement('button', {
                            onClick: handleClosePricingModal,
                            className: 'modal-glass-close-button',
                            'aria-label': 'Close modal'
                        },
                            React.createElement('span', {
                                className: 'iconify',
                                'data-icon': 'solar:close-circle-bold',
                                style: { fontSize: '24px' }
                            })
                        ),

                        // Header Content
                        React.createElement('div', {
                            className: 'modal-glass-header-content'
                        },
                            React.createElement('h2', {
                                className: 'modal-glass-title'
                            }, 'Choose Your Plan'),
                            React.createElement('p', {
                                className: 'modal-glass-subtitle'
                            }, 'Unlock the full potential of AI thumbnail generation with premium features')
                        )
                    ),

                    // Modal Body - Pricing Plans
                    React.createElement('div', {
                        className: 'modal-glass-body'
                    },
                        React.createElement('div', {
                            className: 'pricing-plans-grid'
                        },
                            pricingPlans.map((plan, index) =>
                                React.createElement('div', {
                                    key: plan.id,
                                    className: `pricing-plan-card ${plan.isCurrentPlan ? 'current-plan' : ''
                                        } ${plan.highlight ? 'highlighted-plan' : ''}`,
                                    style: plan.highlight ? {
                                        transform: 'translateY(-8px)',
                                        zIndex: 2
                                    } : {}
                                },
                                    // Plan Badge
                                    plan.mostPopular && React.createElement('div', {
                                        className: 'plan-badge most-popular'
                                    },
                                        React.createElement('span', {
                                            className: 'plan-badge-text'
                                        }, 'Most Popular')
                                    ),

                                    plan.isCurrentPlan && React.createElement('div', {
                                        className: 'plan-badge current-plan-badge'
                                    },
                                        React.createElement('span', {
                                            className: 'plan-badge-text'
                                        }, 'Current Plan')
                                    ),

                                    // Plan Content
                                    React.createElement('div', {
                                        className: 'plan-content'
                                    },
                                        // Plan Header
                                        React.createElement('div', {
                                            className: 'plan-header'
                                        },
                                            React.createElement('h3', {
                                                className: 'plan-name'
                                            }, plan.name),
                                            React.createElement('div', {
                                                className: 'plan-price-container'
                                            },
                                                React.createElement('span', {
                                                    className: 'plan-price'
                                                }, plan.price),
                                                React.createElement('span', {
                                                    className: 'plan-period'
                                                }, plan.period)
                                            ),
                                            React.createElement('p', {
                                                className: 'plan-description'
                                            }, plan.description)
                                        ),

                                        // Features List
                                        React.createElement('ul', {
                                            className: 'plan-features'
                                        },
                                            plan.features.map((feature, featureIndex) =>
                                                React.createElement('li', {
                                                    key: featureIndex,
                                                    className: 'plan-feature'
                                                },
                                                    React.createElement('span', {
                                                        className: 'feature-icon iconify',
                                                        'data-icon': 'solar:check-circle-bold'
                                                    }),
                                                    React.createElement('span', {
                                                        className: 'feature-text'
                                                    }, feature)
                                                )
                                            )
                                        ),

                                        // Action Button
                                        React.createElement('button', {
                                            onClick: () => handlePricingPlanSelect(plan.id),
                                            disabled: plan.isCurrentPlan,
                                            className: `plan-action-button ${plan.isCurrentPlan ? 'disabled' : ''
                                                } ${plan.highlight ? 'highlighted' : 'standard'}`
                                        }, plan.buttonText)
                                    )
                                )
                            )
                        )
                    ),

                    // Modal Footer
                    React.createElement('div', {
                        className: 'modal-glass-footer'
                    },
                        React.createElement('div', {
                            className: 'footer-guarantees'
                        },
                            React.createElement('div', {
                                className: 'guarantee-item'
                            },
                                React.createElement('span', {
                                    className: 'guarantee-icon iconify',
                                    'data-icon': 'solar:shield-check-bold'
                                }),
                                React.createElement('span', {
                                    className: 'guarantee-text'
                                }, '30-day money back guarantee')
                            ),
                            React.createElement('div', {
                                className: 'guarantee-item'
                            },
                                React.createElement('span', {
                                    className: 'guarantee-icon iconify',
                                    'data-icon': 'solar:card-bold'
                                }),
                                React.createElement('span', {
                                    className: 'guarantee-text'
                                }, 'Secure payment processing')
                            ),
                            React.createElement('div', {
                                className: 'guarantee-item'
                            },
                                React.createElement('span', {
                                    className: 'guarantee-icon iconify',
                                    'data-icon': 'solar:refresh-bold'
                                }),
                                React.createElement('span', {
                                    className: 'guarantee-text'
                                }, 'Cancel anytime')
                            )
                        )
                    )
                )
            )
        );
    };

    // Keyboard event handler for Esc key - updated for modal
    useEffect(() => {
        const handleKeyDown = (event) => {
            if (event.key === 'Escape') {
                if (isPricingModalOpen) {
                    handleClosePricingModal();
                }
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [isPricingModalOpen]);

    // NEW: Billing form handlers
    const handleEditBilling = () => {
        // Initialize form with current user data
        setBillingFormData({
            fullName: editableUser.fullName || '',
            email: editableUser.email || '',
            phone: editableUser.phone || '',
            country: editableUser.country || '',
            address: editableUser.address || '',
            postalCode: editableUser.postalCode || ''
        });
        setBillingErrors({});
        setBillingEditMode(true);
    };

    const handleBillingFieldChange = (field, value) => {
        setBillingFormData(prev => ({
            ...prev,
            [field]: value
        }));

        // Clear error for this field when user starts typing
        if (billingErrors[field]) {
            setBillingErrors(prev => ({
                ...prev,
                [field]: ''
            }));
        }
    };

    const validateBillingForm = () => {
        const errors = {};

        // Required fields validation
        if (!billingFormData.fullName.trim()) {
            errors.fullName = 'Full name is required';
        }
        if (!billingFormData.email.trim()) {
            errors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(billingFormData.email)) {
            errors.email = 'Please enter a valid email address';
        }
        if (!billingFormData.country) {
            errors.country = 'Country is required';
        }
        if (!billingFormData.address.trim()) {
            errors.address = 'Address is required';
        }
        if (!billingFormData.postalCode.trim()) {
            errors.postalCode = 'Postal code is required';
        }

        setBillingErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSaveBilling = async () => {
        if (!validateBillingForm()) {
            return;
        }

        setIsSavingBilling(true);

        try {
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Update user data
            const updatedUser = {
                ...editableUser,
                ...billingFormData
            };

            setEditableUser(updatedUser);

            // Update localStorage
            localStorage.setItem('user-profile', JSON.stringify(updatedUser));

            setBillingEditMode(false);
            showLocalToast('Billing information updated successfully!', 'success');

        } catch (error) {
            showLocalToast('Failed to update billing information. Please try again.', 'error');
        } finally {
            setIsSavingBilling(false);
        }
    };

    const handleCancelBilling = () => {
        setBillingEditMode(false);
        setBillingFormData({
            fullName: '',
            email: '',
            phone: '',
            country: '',
            address: '',
            postalCode: ''
        });
        setBillingErrors({});
    };

    // Helper function to get stored user data with fallback
    const getStoredUserData = () => {
        const storedUserData = localStorage.getItem('user-profile');
        return storedUserData ? JSON.parse(storedUserData) : editableUser;
    };

    return React.createElement('div', {
        className: `user-dashboard min-h-screen bg-gray-900 flex flex-col transition-opacity duration-200 ease-in-out ${isClosing ? 'opacity-0 closing' : 'opacity-100'}`
    },
        // Header - Keep full width
        React.createElement('header', { className: 'dashboard-header border-b border-gray-700 bg-gray-800 w-full' },
            React.createElement('div', { className: 'flex items-center justify-between px-4 sm:px-6 py-4 max-w-[1070px] mx-auto' },
                // Left side - Dashboard title
                React.createElement('h1', {
                    className: 'text-white font-semibold',
                    style: { fontSize: '1.5rem' }
                }, 'User Dashboard'),

                // Right side - Back button
                React.createElement('button', {
                    onClick: onExitDashboard,
                    className: 'inline-flex items-center gap-2 px-4 py-2 border border-gray-600 text-gray-300 hover:text-white hover:border-gray-500 rounded-lg transition-all duration-200 font-medium',
                    'aria-label': 'Back to main app'
                },
                    React.createElement(Icon, {
                        icon: 'solar:alt-arrow-left-linear',
                        style: { fontSize: '16px' }
                    }),
                    'Back'
                )
            )
        ),

        // Navigation Tabs - Keep full width background but center content
        React.createElement('nav', { className: 'dashboard-nav border-b border-gray-700 bg-gray-800/50 w-full' },
            React.createElement('div', { className: 'flex px-6 max-w-[960px] mx-auto' },
                tabs.map(tab =>
                    React.createElement('button', {
                        key: tab.id,
                        onClick: () => handleTabChange(tab.id),
                        className: `flex items-center gap-2 px-4 py-3 border-b-2 transition-all duration-250 ${activeTab === tab.id
                                ? 'border-purple-400 text-purple-400'
                                : 'border-transparent text-gray-400 hover:text-white'
                            }`
                    },
                        React.createElement(Icon, {
                            icon: tab.icon,
                            style: { fontSize: '18px' }
                        }),
                        tab.label
                    )
                )
            )
        ),

        // Main Content Container - Centered with 960px max width
        React.createElement('div', { className: 'dashboard-outer-wrapper w-full flex-1 flex justify-center bg-gray-900' },
            React.createElement('div', { className: 'dashboard-container w-full max-w-[960px] px-6' },
                // Tab Content with animation wrapper
                React.createElement('main', {
                    className: 'dashboard-content flex-1 py-6 tab-content-wrapper'
                },
                    React.createElement('div', {
                        key: activeTab,
                        className: 'tab-content'
                    },
                        activeTab === 'overview' && renderOverviewTab(),
                        activeTab === 'account' && renderAccountTab(),
                        activeTab === 'billing' && renderBillingTab(),
                        activeTab === 'history' && renderHistoryTab()
                    )
                )
            )
        ),

        // Confirmation Modal
        React.createElement(ConfirmationModal, {
            isOpen: confirmModal.isOpen,
            onClose: closeConfirmModal,
            onConfirm: confirmModal.onConfirm,
            title: confirmModal.title,
            message: confirmModal.message,
            confirmText: 'Delete',
            cancelText: 'Cancel',
            confirmButtonId: 'delete-confirm-btn',
            cancelButtonId: 'delete-cancel-btn'
        }),

        // Generation Details Modal
        React.createElement(GenerationDetailsModal, {
            isOpen: detailsModal.isOpen,
            onClose: () => {
                setDetailsModal({ isOpen: false, details: null });
            },
            details: detailsModal.details,
            host: 'localhost:3001'
        }),

        // Thumbnail Preview Modal
        React.createElement(ThumbnailPreviewModal, {
            isOpen: previewModal.isOpen,
            onClose: () => {
                setPreviewModal({ isOpen: false, thumbnail: null, title: null, itemId: null, fullImageUrl: null });
            },
            thumbnail: previewModal.thumbnail,
            title: previewModal.title,
            itemId: previewModal.itemId,
            fullImageUrl: previewModal.fullImageUrl
        }),

        // Password Change Modal
        React.createElement(PasswordChangeModal, {
            isOpen: passwordModal.isOpen,
            onClose: () => {
                setPasswordModal(prev => ({ ...prev, isOpen: false }));
            },
            onPasswordChange: handlePasswordChange,
            passwordState: passwordModal,
            setPasswordState: setPasswordModal,
            rateLimitInfo: passwordRateLimit
        }),

        // Avatar Upload Modal
        React.createElement(AvatarUploadModal, {
            isOpen: uploadModal.isOpen,
            onClose: () => {
                setUploadModal({ isOpen: false });
            },
            onAvatarUpload: handleAvatarUpload,
            avatarFile: avatarFile,
            setAvatarFile: setAvatarFile,
            avatarPreview: avatarPreview,
            setAvatarPreview: setAvatarPreview
        }),

        // Premium Local Toast Notification - Hero UI & macOS Liquid Glass Style
        localToast.isVisible && React.createElement('div', {
            className: 'dashboard-liquid-toast-container fixed top-6 right-6 z-[10001] transform transition-all ease-out',
            style: {
                animation: localToast.isVisible ? 'liquidSlideInRight 495ms cubic-bezier(0.25, 0.46, 0.45, 0.94)' : 'liquidSlideOutRight 495ms cubic-bezier(0.94, 0.45, 0.46, 0.25)',
                opacity: localToast.isVisible ? 1 : 0,
                transition: 'opacity 495ms cubic-bezier(0.25, 0.46, 0.45, 0.94)'
            }
        },
            React.createElement('div', {
                className: `liquid-glass-toast-inverted border backdrop-blur-md rounded-2xl p-5 flex items-center gap-4 min-w-[400px] max-w-[520px] transition-all duration-300 ease-out`,
                style: {
                    backdropFilter: (() => {
                        switch (localToast.type) {
                            case 'success':
                                return 'blur(20px) saturate(180%) brightness(1.1) contrast(1.05)';
                            case 'warning':
                                return 'blur(20px) saturate(170%) brightness(1.05) contrast(1.08)';
                            case 'error':
                                return 'blur(20px) saturate(160%) brightness(0.95) contrast(1.1)';
                            case 'info':
                                return 'blur(20px) saturate(160%)'; // Keep original for info
                            default:
                                return 'blur(20px) saturate(160%)';
                        }
                    })(),
                    WebkitBackdropFilter: (() => {
                        switch (localToast.type) {
                            case 'success':
                                return 'blur(20px) saturate(180%) brightness(1.1) contrast(1.05)';
                            case 'warning':
                                return 'blur(20px) saturate(170%) brightness(1.05) contrast(1.08)';
                            case 'error':
                                return 'blur(20px) saturate(160%) brightness(0.95) contrast(1.1)';
                            case 'info':
                                return 'blur(20px) saturate(160%)'; // Keep original for info
                            default:
                                return 'blur(20px) saturate(160%)';
                        }
                    })(),
                    background: (() => {
                        switch (localToast.type) {
                            case 'success':
                                return 'linear-gradient(135deg, #16A34A 0%, #15803D 100%)'; // Bold green gradient
                            case 'info':
                                return 'linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%)'; // Bold blue gradient
                            case 'warning':
                                return 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)'; // Bold amber gradient
                            case 'error':
                                return 'linear-gradient(135deg, #DC2626 0%, #B91C1C 100%)'; // Bold red gradient
                            default:
                                return 'linear-gradient(135deg, #16A34A 0%, #15803D 100%)';
                        }
                    })(),
                    border: (() => {
                        switch (localToast.type) {
                            case 'success':
                                return '1px solid rgba(255, 255, 255, 0.2)'; // White border for contrast
                            case 'info':
                                return '1px solid rgba(255, 255, 255, 0.2)';
                            case 'warning':
                                return '1px solid rgba(255, 255, 255, 0.2)';
                            case 'error':
                                return '1px solid rgba(255, 255, 255, 0.2)';
                            default:
                                return '1px solid rgba(255, 255, 255, 0.2)';
                        }
                    })(),
                    boxShadow: (() => {
                        switch (localToast.type) {
                            case 'success':
                                return '0 25px 50px -12px rgba(22, 163, 74, 0.4), 0 8px 16px -8px rgba(22, 163, 74, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                            case 'info':
                                return '0 25px 50px -12px rgba(37, 99, 235, 0.4), 0 8px 16px -8px rgba(37, 99, 235, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                            case 'warning':
                                return '0 25px 50px -12px rgba(245, 158, 11, 0.4), 0 8px 16px -8px rgba(245, 158, 11, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                            case 'error':
                                return '0 25px 50px -12px rgba(220, 38, 38, 0.4), 0 8px 16px -8px rgba(220, 38, 38, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                            default:
                                return '0 25px 50px -12px rgba(22, 163, 74, 0.4), 0 8px 16px -8px rgba(22, 163, 74, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)';
                        }
                    })(),
                    borderRadius: '16px'
                }
            },
                // Enhanced Icon with macOS styling
                React.createElement('div', {
                    className: 'flex-shrink-0 liquid-toast-icon-container',
                    style: {
                        width: '44px',
                        height: '44px',
                        borderRadius: '12px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        background: 'rgba(255, 255, 255, 0.15)', // Semi-transparent white background
                        border: '1px solid rgba(255, 255, 255, 0.25)', // White border for contrast
                        backdropFilter: 'blur(8px)',
                        WebkitBackdropFilter: 'blur(8px)'
                    }
                },
                    React.createElement('span', {
                        className: 'iconify',
                        'data-icon': (() => {
                            switch (localToast.type) {
                                case 'success':
                                    return 'solar:check-circle-bold';
                                case 'info':
                                    return 'solar:info-circle-bold';
                                case 'warning':
                                    return 'solar:danger-triangle-bold';
                                case 'error':
                                    return 'solar:danger-triangle-bold'; // Changed from close-circle to warning triangle
                                default:
                                    return 'solar:check-circle-bold';
                            }
                        })(),
                        style: {
                            fontSize: '22px', // Optimized for new container
                            color: '#ffffff' // Pure white for maximum contrast
                        }
                    })
                ),

                // Enhanced Message content with improved typography
                React.createElement('div', {
                    className: 'flex-1 liquid-toast-content'
                },
                    React.createElement('p', {
                        className: `text-base font-semibold leading-6 tracking-tight text-white`,
                        style: {
                            fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", system-ui, sans-serif',
                            fontSize: '15px',
                            fontWeight: '600',
                            lineHeight: '1.4',
                            letterSpacing: '-0.01em',
                            color: '#ffffff' // Pure white for maximum contrast and accessibility
                        }
                    }, localToast.message)
                ),

                // Premium Close Button - macOS inspired with liquid animations
                React.createElement('button', {
                    onClick: () => {
                        // Enhanced liquid close animation with consistent timing
                        const toastElement = document.querySelector('.dashboard-liquid-toast-container');
                        if (toastElement) {
                            toastElement.style.animation = 'liquidSlideOutRight 495ms cubic-bezier(0.94, 0.45, 0.46, 0.25)';
                            toastElement.style.opacity = '0';
                            toastElement.style.transition = 'opacity 495ms cubic-bezier(0.94, 0.45, 0.46, 0.25)';
                        }

                        // Call the dismiss handler with consistent timing
                        setTimeout(() => {
                            setLocalToast(prev => ({ ...prev, isVisible: false }));
                        }, 80); // Adjusted delay for smoother transition
                    },
                    className: 'liquid-close-button flex-shrink-0 transition-all duration-300 ease-out focus:outline-none',
                    'aria-label': 'Close notification',
                    style: {
                        width: '32px',
                        height: '32px',
                        borderRadius: '10px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        background: 'rgba(255, 255, 255, 0.15)',
                        border: '1px solid rgba(255, 255, 255, 0.25)',
                        backdropFilter: 'blur(8px)',
                        WebkitBackdropFilter: 'blur(8px)',
                        transition: 'all 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                        cursor: 'pointer'
                    },
                    onMouseEnter: (e) => {
                        e.target.style.background = 'rgba(255, 255, 255, 0.25)';
                        e.target.style.border = '1px solid rgba(255, 255, 255, 0.35)';
                        e.target.style.transform = 'scale(1.05)';
                        e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                    },
                    onMouseLeave: (e) => {
                        e.target.style.background = 'rgba(255, 255, 255, 0.15)';
                        e.target.style.border = '1px solid rgba(255, 255, 255, 0.25)';
                        e.target.style.transform = 'scale(1)';
                        e.target.style.boxShadow = 'none';
                    },
                    onFocus: (e) => {
                        e.target.style.outline = '2px solid rgba(59, 130, 246, 0.5)';
                        e.target.style.outlineOffset = '2px';
                    },
                    onBlur: (e) => {
                        e.target.style.outline = 'none';
                    }
                },
                    React.createElement('span', {
                        className: 'iconify',
                        'data-icon': 'solar:close-circle-bold',
                        style: {
                            fontSize: '18px', // Optimized for new container size
                            color: '#ffffff', // Pure white for visibility on colored backgrounds
                            transition: 'all 200ms ease-out'
                        }
                    })
                )
            )
        ),
        renderPricingModal()
    );
}; 